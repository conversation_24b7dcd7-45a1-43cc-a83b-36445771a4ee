'use strict'

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('user_favorites', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      recipe_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '菜谱ID',
        references: {
          model: 'recipes',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '用户备注'
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '用户自定义标签'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    })

    // 添加索引
    await queryInterface.addIndex('user_favorites', ['user_id', 'recipe_id'], {
      unique: true,
      name: 'user_favorites_user_recipe_unique'
    })
    await queryInterface.addIndex('user_favorites', ['user_id'])
    await queryInterface.addIndex('user_favorites', ['recipe_id'])
    await queryInterface.addIndex('user_favorites', ['created_at'])
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('user_favorites')
  }
}
