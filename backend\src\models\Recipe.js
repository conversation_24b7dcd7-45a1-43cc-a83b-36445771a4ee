const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Recipe = sequelize.define('Recipe', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '菜谱名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '菜谱描述'
  },
  ingredients: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: '食材列表',
    defaultValue: []
    // 格式: [{ name: '鸡蛋', amount: '2个', id: 1 }]
  },
  steps: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: '制作步骤',
    defaultValue: []
    // 格式: [{ step: 1, description: '...', image: '', time: 5 }]
  },
  tips: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '制作小贴士'
  },
  nutrition: {
    type: DataTypes.JSO<PERSON>,
    allowNull: true,
    comment: '营养信息',
    defaultValue: {
      total_calories: 0,
      per_serving: {
        calories: 0,
        protein: 0,
        fat: 0,
        carbs: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0
      }
    }
  },
  difficulty: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 1,
    comment: '难度等级 1-简单 2-中等 3-困难'
  },
  cook_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '制作时间(分钟)'
  },
  prep_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '准备时间(分钟)'
  },
  servings: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '份数'
  },
  cuisine_type: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '菜系类型'
  },
  meal_type: {
    type: DataTypes.ENUM('breakfast', 'lunch', 'dinner', 'snack', 'dessert'),
    allowNull: true,
    comment: '餐食类型'
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '标签',
    defaultValue: []
  },
  image_url: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '菜谱主图URL'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '菜谱图片列表',
    defaultValue: []
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    defaultValue: 0,
    comment: '平均评分'
  },
  rating_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '评分次数'
  },
  view_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '查看次数'
  },
  favorite_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '收藏次数'
  },
  source: {
    type: DataTypes.ENUM('ai_generated', 'user_created', 'imported'),
    defaultValue: 'ai_generated',
    comment: '来源'
  },
  ai_model: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'AI模型名称'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'pending'),
    defaultValue: 'active',
    comment: '状态'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'recipes',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['difficulty']
    },
    {
      fields: ['cuisine_type']
    },
    {
      fields: ['meal_type']
    },
    {
      fields: ['rating']
    },
    {
      fields: ['source']
    },
    {
      fields: ['status']
    },
    {
      fields: ['created_at']
    }
  ]
})

// 实例方法
Recipe.prototype.incrementViewCount = function () {
  return this.increment('view_count')
}

Recipe.prototype.updateRating = async function (newRating) {
  const currentRating = this.rating || 0
  const currentCount = this.rating_count || 0
  
  const totalRating = currentRating * currentCount + newRating
  const newCount = currentCount + 1
  const avgRating = totalRating / newCount
  
  return this.update({
    rating: Math.round(avgRating * 100) / 100,
    rating_count: newCount
  })
}

// 类方法
Recipe.findByIngredients = function (ingredientIds) {
  return this.findAll({
    where: {
      status: 'active',
      [sequelize.Op.and]: ingredientIds.map(id => 
        sequelize.literal(`JSON_SEARCH(ingredients, 'one', '${id}', NULL, '$[*].id') IS NOT NULL`)
      )
    }
  })
}

Recipe.findByDifficulty = function (difficulty) {
  return this.findAll({
    where: {
      difficulty: difficulty,
      status: 'active'
    }
  })
}

Recipe.findPopular = function (limit = 10) {
  return this.findAll({
    where: { status: 'active' },
    order: [
      ['rating', 'DESC'],
      ['favorite_count', 'DESC'],
      ['view_count', 'DESC']
    ],
    limit: limit
  })
}

module.exports = Recipe
