'use strict'

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('recipes', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: '菜谱名称'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '菜谱描述'
      },
      ingredients: {
        type: Sequelize.JSON,
        allowNull: false,
        comment: '食材列表'
      },
      steps: {
        type: Sequelize.JSON,
        allowNull: false,
        comment: '制作步骤'
      },
      tips: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '制作小贴士'
      },
      nutrition: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '营养信息'
      },
      difficulty: {
        type: Sequelize.TINYINT,
        allowNull: false,
        defaultValue: 1,
        comment: '难度等级 1-简单 2-中等 3-困难'
      },
      cook_time: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '制作时间(分钟)'
      },
      prep_time: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '准备时间(分钟)'
      },
      servings: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: '份数'
      },
      cuisine_type: {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: '菜系类型'
      },
      meal_type: {
        type: Sequelize.ENUM('breakfast', 'lunch', 'dinner', 'snack', 'dessert'),
        allowNull: true,
        comment: '餐食类型'
      },
      tags: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '标签'
      },
      image_url: {
        type: Sequelize.STRING(200),
        allowNull: true,
        comment: '菜谱主图URL'
      },
      images: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '菜谱图片列表'
      },
      rating: {
        type: Sequelize.DECIMAL(3, 2),
        allowNull: true,
        defaultValue: 0,
        comment: '平均评分'
      },
      rating_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '评分次数'
      },
      view_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '查看次数'
      },
      favorite_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '收藏次数'
      },
      source: {
        type: Sequelize.ENUM('ai_generated', 'user_created', 'imported'),
        defaultValue: 'ai_generated',
        comment: '来源'
      },
      ai_model: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'AI模型名称'
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'pending'),
        defaultValue: 'active',
        comment: '状态'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    })

    // 添加索引
    await queryInterface.addIndex('recipes', ['name'])
    await queryInterface.addIndex('recipes', ['difficulty'])
    await queryInterface.addIndex('recipes', ['cuisine_type'])
    await queryInterface.addIndex('recipes', ['meal_type'])
    await queryInterface.addIndex('recipes', ['rating'])
    await queryInterface.addIndex('recipes', ['source'])
    await queryInterface.addIndex('recipes', ['status'])
    await queryInterface.addIndex('recipes', ['created_at'])
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('recipes')
  }
}
