const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const UserFavorite = sequelize.define('UserFavorite', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  recipe_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '菜谱ID',
    references: {
      model: 'recipes',
      key: 'id'
    }
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户备注'
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '用户自定义标签',
    defaultValue: []
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'user_favorites',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['user_id', 'recipe_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['recipe_id']
    },
    {
      fields: ['created_at']
    }
  ]
})

// 类方法
UserFavorite.findByUser = function (userId, limit = 20) {
  return this.findAll({
    where: { user_id: userId },
    include: [{
      model: require('./Recipe'),
      as: 'recipe'
    }],
    order: [['created_at', 'DESC']],
    limit: limit
  })
}

UserFavorite.isFavorited = async function (userId, recipeId) {
  const favorite = await this.findOne({
    where: {
      user_id: userId,
      recipe_id: recipeId
    }
  })
  return !!favorite
}

UserFavorite.toggleFavorite = async function (userId, recipeId, notes = null) {
  const existing = await this.findOne({
    where: {
      user_id: userId,
      recipe_id: recipeId
    }
  })

  if (existing) {
    // 取消收藏
    await existing.destroy()
    return { action: 'removed', favorite: null }
  } else {
    // 添加收藏
    const favorite = await this.create({
      user_id: userId,
      recipe_id: recipeId,
      notes: notes
    })
    return { action: 'added', favorite: favorite }
  }
}

module.exports = UserFavorite
