const axios = require('axios')
const crypto = require('crypto')
const BaseImageRecognition = require('../base/BaseImageRecognition')

/**
 * 阿里云AI图像识别服务适配器
 */
class AliyunImageRecognition extends BaseImageRecognition {
  constructor (config) {
    super(config)
    this.region = config.region || 'cn-shanghai'
    this.endpoint = `https://objectdet.${this.region}.aliyuncs.com`
  }

  /**
   * 识别图片中的食材
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeIngredients (image, options = {}) {
    try {
      const base64Image = this.preprocessImage(image)
      
      const params = {
        Action: 'DetectObject',
        Version: '2019-12-30',
        RegionId: this.region,
        ImageURL: `data:image/jpeg;base64,${base64Image}`
      }
      
      const response = await this.makeRequest(params)
      return this.normalizeIngredientsResult(response.data)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 识别菜品
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeDish (image, options = {}) {
    try {
      const base64Image = this.preprocessImage(image)
      
      const params = {
        Action: 'ClassifyingRubbish',
        Version: '2019-12-30',
        RegionId: this.region,
        ImageURL: `data:image/jpeg;base64,${base64Image}`
      }
      
      const response = await this.makeRequest(params)
      return this.normalizeDishResult(response.data)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 通用物体识别
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeObject (image, options = {}) {
    return this.recognizeIngredients(image, options)
  }

  /**
   * 发起API请求
   * @param {Object} params - 请求参数
   * @returns {Promise<Object>} 响应结果
   */
  async makeRequest (params) {
    const timestamp = new Date().toISOString()
    const nonce = Math.random().toString(36).substring(2, 15)
    
    // 构建签名参数
    const signParams = {
      ...params,
      AccessKeyId: this.apiKey,
      SignatureMethod: 'HMAC-SHA1',
      Timestamp: timestamp,
      SignatureVersion: '1.0',
      SignatureNonce: nonce,
      Format: 'JSON'
    }
    
    // 生成签名
    const signature = this.generateSignature(signParams, 'POST')
    signParams.Signature = signature
    
    const response = await axios.post(this.endpoint, signParams, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
    
    return response
  }

  /**
   * 生成API签名
   * @param {Object} params - 参数对象
   * @param {string} method - HTTP方法
   * @returns {string} 签名
   */
  generateSignature (params, method) {
    // 排序参数
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    // 构建签名字符串
    const stringToSign = `${method}&${encodeURIComponent('/')}&${encodeURIComponent(sortedParams)}`
    
    // 生成签名
    const signature = crypto
      .createHmac('sha1', this.secretKey + '&')
      .update(stringToSign)
      .digest('base64')
    
    return signature
  }

  /**
   * 标准化食材识别结果
   * @param {Object} rawResult - 阿里云原始结果
   * @returns {Object} 标准化结果
   */
  normalizeIngredientsResult (rawResult) {
    const ingredients = []
    
    if (rawResult.Data && rawResult.Data.Elements) {
      for (const element of rawResult.Data.Elements) {
        if (this.isFoodRelated(element.Type)) {
          ingredients.push({
            name: element.Type,
            confidence: element.Score,
            category: this.categorizeIngredient(element.Type),
            box: element.Boxes ? element.Boxes[0] : null
          })
        }
      }
    }

    return this.normalizeResult({
      ingredients,
      confidence: ingredients.length > 0 ? ingredients[0].confidence : 0,
      raw_result: rawResult
    })
  }

  /**
   * 标准化菜品识别结果
   * @param {Object} rawResult - 阿里云原始结果
   * @returns {Object} 标准化结果
   */
  normalizeDishResult (rawResult) {
    const dishes = []
    
    if (rawResult.Data && rawResult.Data.Elements) {
      for (const element of rawResult.Data.Elements) {
        dishes.push({
          name: element.Category,
          confidence: element.Score,
          category: element.CategoryScore || null
        })
      }
    }

    return this.normalizeResult({
      dishes,
      confidence: dishes.length > 0 ? dishes[0].confidence : 0,
      raw_result: rawResult
    })
  }

  /**
   * 判断是否为食物相关
   * @param {string} type - 类型名称
   * @returns {boolean} 是否为食物相关
   */
  isFoodRelated (type) {
    const foodTypes = [
      'food', 'fruit', 'vegetable', 'meat', 'seafood', 'grain',
      '食物', '水果', '蔬菜', '肉类', '海鲜', '谷物', '豆类'
    ]
    
    return foodTypes.some(foodType => 
      type.toLowerCase().includes(foodType.toLowerCase())
    )
  }

  /**
   * 食材分类
   * @param {string} ingredient - 食材名称
   * @returns {string} 分类
   */
  categorizeIngredient (ingredient) {
    const categories = {
      蔬菜: ['vegetable', '蔬菜', '菜'],
      水果: ['fruit', '水果', '果'],
      肉类: ['meat', '肉类', '肉'],
      海鲜: ['seafood', '海鲜', '鱼', '虾'],
      谷物: ['grain', '谷物', '米', '面'],
      其他: ['food', '食物']
    }
    
    for (const [category, keywords] of Object.entries(categories)) {
      for (const keyword of keywords) {
        if (ingredient.toLowerCase().includes(keyword.toLowerCase())) {
          return category
        }
      }
    }
    
    return '其他'
  }

  /**
   * 计算调用成本
   * @param {Object} result - 识别结果
   * @returns {number} 成本（元）
   */
  calculateCost (result) {
    // 阿里云图像识别按次计费，约0.002元/次
    return 0.002
  }
}

module.exports = AliyunImageRecognition
