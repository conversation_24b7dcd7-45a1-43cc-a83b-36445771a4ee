const express = require('express')
const multer = require('multer')
const router = express.Router()
const aiController = require('../controllers/aiController')
const authMiddleware = require('../middleware/auth')
const { fileValidationMiddleware } = require('../middleware/validation')
const { body, query } = require('express-validator')
const validationMiddleware = require('../middleware/validation').default

// 配置文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('不支持的文件类型'))
    }
  }
})

// 验证规则
const generateRecipeValidation = [
  body('ingredients')
    .isArray({ min: 1 })
    .withMessage('食材列表不能为空'),
  body('ingredients.*')
    .isString()
    .isLength({ min: 1, max: 50 })
    .withMessage('食材名称长度必须在1-50个字符之间'),
  body('preferences')
    .optional()
    .isObject()
    .withMessage('偏好设置必须是对象格式'),
  body('preferences.cuisine_type')
    .optional()
    .isString()
    .isLength({ max: 20 })
    .withMessage('菜系类型长度不能超过20个字符'),
  body('preferences.difficulty')
    .optional()
    .isIn(['简单', '中等', '困难'])
    .withMessage('难度必须是：简单、中等、困难之一'),
  body('preferences.cook_time')
    .optional()
    .isInt({ min: 5, max: 300 })
    .withMessage('制作时间必须在5-300分钟之间'),
  body('preferences.servings')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('份数必须在1-20之间'),
  body('provider')
    .optional()
    .isString()
    .isIn(['qwen', 'spark', 'wenxin'])
    .withMessage('文本生成服务提供商必须是：qwen、spark、wenxin之一')
]

const usageStatsValidation = [
  query('startDate')
    .isISO8601()
    .withMessage('开始日期格式不正确'),
  query('endDate')
    .isISO8601()
    .withMessage('结束日期格式不正确')
]

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间')
]

// 所有AI相关路由都需要身份验证
router.use(authMiddleware)

// 图像识别接口
router.post('/recognize-image',
  upload.single('image'),
  fileValidationMiddleware({
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxSize: 5 * 1024 * 1024,
    required: true
  }),
  query('provider')
    .optional()
    .isString()
    .isIn(['baidu', 'tencent', 'aliyun'])
    .withMessage('图像识别服务提供商必须是：baidu、tencent、aliyun之一'),
  validationMiddleware,
  aiController.recognizeImage
)

// 生成菜谱接口
router.post('/generate-recipe',
  generateRecipeValidation,
  validationMiddleware,
  aiController.generateRecipe
)

// 获取AI服务状态
router.get('/services/status',
  aiController.getServicesStatus
)

// 获取AI使用统计（管理员功能）
router.get('/usage/stats',
  usageStatsValidation,
  validationMiddleware,
  aiController.getUsageStats
)

// 获取用户识别历史记录
router.get('/recognition/history',
  paginationValidation,
  validationMiddleware,
  aiController.getRecognitionHistory
)

// 获取识别记录详情
router.get('/recognition/:recordId',
  async (req, res) => {
    try {
      const { userId } = req.user
      const { recordId } = req.params
      
      const record = await RecognitionRecord.findOne({
        where: {
          id: recordId,
          user_id: userId
        }
      })
      
      if (!record) {
        return res.status(404).json({
          success: false,
          message: '识别记录不存在'
        })
      }
      
      res.json({
        success: true,
        message: '识别记录获取成功',
        data: record
      })
    } catch (error) {
      console.error('获取识别记录详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取识别记录失败',
        error: error.message
      })
    }
  }
)

module.exports = router
