/**
 * 图像识别服务基础抽象类
 */
class BaseImageRecognition {
  constructor (config) {
    this.config = config
    this.provider = config.provider
    this.apiKey = config.apiKey
    this.secretKey = config.secretKey
    this.baseUrl = config.baseUrl
  }

  /**
   * 识别图片中的食材
   * @param {string|Buffer} image - 图片数据（base64字符串或Buffer）
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeIngredients (image, options = {}) {
    throw new Error('recognizeIngredients method must be implemented')
  }

  /**
   * 识别菜品
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeDish (image, options = {}) {
    throw new Error('recognizeDish method must be implemented')
  }

  /**
   * 通用物体识别
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeObject (image, options = {}) {
    throw new Error('recognizeObject method must be implemented')
  }

  /**
   * 预处理图片数据
   * @param {string|Buffer} image - 图片数据
   * @returns {string} base64编码的图片数据
   */
  preprocessImage (image) {
    if (Buffer.isBuffer(image)) {
      return image.toString('base64')
    }
    
    if (typeof image === 'string') {
      // 如果已经是base64，直接返回
      if (image.startsWith('data:image/')) {
        return image.split(',')[1]
      }
      return image
    }
    
    throw new Error('Invalid image format')
  }

  /**
   * 标准化识别结果
   * @param {Object} rawResult - 原始识别结果
   * @returns {Object} 标准化结果
   */
  normalizeResult (rawResult) {
    return {
      success: true,
      provider: this.provider,
      timestamp: new Date().toISOString(),
      data: {
        ingredients: [],
        confidence: 0,
        raw_result: rawResult
      }
    }
  }

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @returns {Object} 标准化错误结果
   */
  handleError (error) {
    console.error(`${this.provider} AI服务错误:`, error)
    
    return {
      success: false,
      provider: this.provider,
      timestamp: new Date().toISOString(),
      error: {
        message: error.message,
        code: error.code || 'UNKNOWN_ERROR',
        details: error.response?.data || null
      }
    }
  }

  /**
   * 计算调用成本
   * @param {Object} result - 识别结果
   * @returns {number} 成本（元）
   */
  calculateCost (result) {
    // 子类实现具体的成本计算逻辑
    return 0
  }

  /**
   * 验证配置
   * @returns {boolean} 配置是否有效
   */
  validateConfig () {
    if (!this.apiKey) {
      throw new Error(`${this.provider} API Key is required`)
    }
    return true
  }

  /**
   * 获取服务状态
   * @returns {Promise<Object>} 服务状态
   */
  async getStatus () {
    try {
      // 子类可以重写此方法来检查服务状态
      return {
        provider: this.provider,
        status: 'available',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        provider: this.provider,
        status: 'unavailable',
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }
}

module.exports = BaseImageRecognition
