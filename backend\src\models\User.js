const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  openid: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '微信小程序用户唯一标识'
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '用户昵称'
  },
  avatar: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '用户头像URL'
  },
  preferences: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '用户偏好设置',
    defaultValue: {
      taste: [], // 口味偏好
      difficulty: 1, // 难度偏好 1-简单 2-中等 3-困难
      allergies: [], // 过敏信息
      dietary: [] // 饮食习惯 vegetarian, vegan, etc.
    }
  },
  health_profile: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '健康档案',
    defaultValue: {
      height: null,
      weight: null,
      age: null,
      gender: null,
      activity_level: null,
      health_goals: []
    }
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'banned'),
    defaultValue: 'active',
    comment: '用户状态'
  },
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后登录时间'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'users',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['openid']
    },
    {
      fields: ['status']
    },
    {
      fields: ['created_at']
    }
  ]
})

// 实例方法
User.prototype.toJSON = function () {
  const values = Object.assign({}, this.get())
  // 不返回敏感信息
  delete values.openid
  return values
}

// 类方法
User.findByOpenid = function (openid) {
  return this.findOne({
    where: { openid }
  })
}

User.createOrUpdate = async function (userData) {
  const [user, created] = await this.findOrCreate({
    where: { openid: userData.openid },
    defaults: userData
  })

  if (!created && userData.nickname) {
    await user.update({
      nickname: userData.nickname,
      avatar: userData.avatar,
      last_login_at: new Date()
    })
  }

  return user
}

module.exports = User
