// 首页逻辑
import Toast from '@vant/weapp/toast/toast'

Page({
  data: {
    loading: false,
    hasUsed: false,
    recommendRecipes: [],
    recentRecognitions: []
  },

  onLoad() {
    this.checkUserStatus()
    this.loadRecommendRecipes()
    this.loadRecentRecognitions()
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadRecentRecognitions()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  /**
   * 检查用户使用状态
   */
  checkUserStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    const hasUsed = wx.getStorageSync('hasUsed') || false
    
    this.setData({
      hasUsed: hasUsed
    })
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    try {
      await Promise.all([
        this.loadRecommendRecipes(),
        this.loadRecentRecognitions()
      ])
      
      wx.stopPullDownRefresh()
      Toast.success('刷新成功')
    } catch (error) {
      console.error('刷新数据失败:', error)
      wx.stopPullDownRefresh()
      Toast.fail('刷新失败')
    }
  },

  /**
   * 加载推荐菜谱
   */
  async loadRecommendRecipes() {
    try {
      this.setData({ loading: true })
      
      const app = getApp()
      const response = await app.request({
        url: '/api/recipes/recommend',
        method: 'GET',
        data: {
          limit: 10
        }
      })

      if (response.success) {
        this.setData({
          recommendRecipes: response.data.recipes || []
        })
      }
    } catch (error) {
      console.error('加载推荐菜谱失败:', error)
      Toast.fail('加载推荐菜谱失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 加载最近识别记录
   */
  async loadRecentRecognitions() {
    try {
      const app = getApp()
      const response = await app.request({
        url: '/api/ai/recognition/history',
        method: 'GET',
        data: {
          page: 1,
          limit: 5
        }
      })

      if (response.success) {
        const recognitions = response.data.records.map(record => ({
          id: record.id,
          image_url: record.image_url,
          ingredients: this.formatIngredients(record.recognition_result?.ingredients || []),
          time: this.formatTime(record.created_at)
        }))

        this.setData({
          recentRecognitions: recognitions
        })
      }
    } catch (error) {
      console.error('加载识别历史失败:', error)
      // 不显示错误提示，因为可能是用户未登录
    }
  },

  /**
   * 格式化食材列表
   */
  formatIngredients(ingredients) {
    if (!ingredients || ingredients.length === 0) {
      return '未识别到食材'
    }
    
    return ingredients.slice(0, 3).map(item => item.name).join('、') + 
           (ingredients.length > 3 ? '等' : '')
  },

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    const time = new Date(timeStr)
    const now = new Date()
    const diff = now - time
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else {
      return `${Math.floor(diff / 86400000)}天前`
    }
  },

  /**
   * 跳转到拍照页面
   */
  goToCamera() {
    wx.navigateTo({
      url: '/pages/camera/camera'
    })
  },

  /**
   * 跳转到菜谱大全
   */
  goToRecipes() {
    wx.switchTab({
      url: '/pages/recipe/recipe'
    })
  },

  /**
   * 跳转到收藏页面
   */
  goToFavorites() {
    wx.navigateTo({
      url: '/pages/profile/favorites/favorites'
    })
  },

  /**
   * 跳转到识别历史
   */
  goToHistory() {
    wx.navigateTo({
      url: '/pages/profile/history/history'
    })
  },

  /**
   * 跳转到个人中心
   */
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  /**
   * 跳转到菜谱详情
   */
  goToRecipeDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/recipe/detail/detail?id=${id}`
    })
  },

  /**
   * 跳转到识别记录详情
   */
  goToRecognitionDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/result/result?recordId=${id}`
    })
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '智能菜谱助手 - 拍照识别食材，AI生成美味菜谱',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.png'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '智能菜谱助手 - 拍照识别食材，AI生成美味菜谱',
      imageUrl: '/images/share-cover.png'
    }
  }
})
