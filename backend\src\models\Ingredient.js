const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const Ingredient = sequelize.define('Ingredient', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '食材名称'
  },
  category: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '食材分类',
    defaultValue: 'other'
  },
  aliases: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '食材别名',
    defaultValue: []
  },
  nutrition_info: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '营养信息',
    defaultValue: {
      calories: 0, // 卡路里/100g
      protein: 0, // 蛋白质/100g
      fat: 0, // 脂肪/100g
      carbs: 0, // 碳水化合物/100g
      fiber: 0, // 纤维/100g
      sugar: 0, // 糖分/100g
      sodium: 0, // 钠/100g
      vitamins: {}, // 维生素
      minerals: {} // 矿物质
    }
  },
  storage_info: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '储存信息',
    defaultValue: {
      storage_method: '', // 储存方法
      shelf_life: '', // 保质期
      temperature: '', // 储存温度
      tips: '' // 储存小贴士
    }
  },
  season: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '应季信息',
    defaultValue: []
  },
  image_url: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '食材图片URL'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active',
    comment: '状态'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'ingredients',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['name']
    },
    {
      fields: ['category']
    },
    {
      fields: ['status']
    }
  ]
})

// 类方法
Ingredient.findByName = function (name) {
  return this.findOne({
    where: {
      [sequelize.Op.or]: [
        { name: name },
        sequelize.literal(`JSON_CONTAINS(aliases, '"${name}"')`)
      ]
    }
  })
}

Ingredient.findByCategory = function (category) {
  return this.findAll({
    where: {
      category: category,
      status: 'active'
    }
  })
}

Ingredient.searchByKeyword = function (keyword) {
  return this.findAll({
    where: {
      [sequelize.Op.or]: [
        { name: { [sequelize.Op.like]: `%${keyword}%` } },
        sequelize.literal(`JSON_SEARCH(aliases, 'one', '%${keyword}%') IS NOT NULL`)
      ],
      status: 'active'
    }
  })
}

module.exports = Ingredient
