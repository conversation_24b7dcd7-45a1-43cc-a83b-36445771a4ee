'use strict'

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('ingredients', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: '食材名称'
      },
      category: {
        type: Sequelize.STRING(20),
        allowNull: true,
        defaultValue: 'other',
        comment: '食材分类'
      },
      aliases: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '食材别名'
      },
      nutrition_info: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '营养信息'
      },
      storage_info: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '储存信息'
      },
      season: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '应季信息'
      },
      image_url: {
        type: Sequelize.STRING(200),
        allowNull: true,
        comment: '食材图片URL'
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive'),
        defaultValue: 'active',
        comment: '状态'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    })

    // 添加索引
    await queryInterface.addIndex('ingredients', ['name'])
    await queryInterface.addIndex('ingredients', ['category'])
    await queryInterface.addIndex('ingredients', ['status'])
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('ingredients')
  }
}
