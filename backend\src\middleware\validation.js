const { validationResult } = require('express-validator')

// 验证中间件
const validationMiddleware = (req, res, next) => {
  const errors = validationResult(req)
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value
    }))
    
    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors: errorMessages
    })
  }
  
  next()
}

// 文件上传验证中间件
const fileValidationMiddleware = (options = {}) => {
  const {
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxSize = 5 * 1024 * 1024, // 5MB
    required = true
  } = options
  
  return (req, res, next) => {
    if (!req.file && required) {
      return res.status(400).json({
        success: false,
        message: '请上传文件'
      })
    }
    
    if (req.file) {
      // 检查文件类型
      if (!allowedTypes.includes(req.file.mimetype)) {
        return res.status(400).json({
          success: false,
          message: `不支持的文件类型，支持的类型: ${allowedTypes.join(', ')}`
        })
      }
      
      // 检查文件大小
      if (req.file.size > maxSize) {
        return res.status(400).json({
          success: false,
          message: `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`
        })
      }
    }
    
    next()
  }
}

// 分页参数验证中间件
const paginationMiddleware = (req, res, next) => {
  const page = parseInt(req.query.page) || 1
  const limit = parseInt(req.query.limit) || 20
  
  // 限制分页参数范围
  req.pagination = {
    page: Math.max(1, page),
    limit: Math.min(100, Math.max(1, limit))
  }
  
  req.pagination.offset = (req.pagination.page - 1) * req.pagination.limit
  
  next()
}

// API限流验证中间件
const rateLimitMiddleware = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15分钟
    maxRequests = 100,
    message = '请求过于频繁，请稍后再试'
  } = options
  
  const requests = new Map()
  
  return (req, res, next) => {
    const key = req.ip || req.connection.remoteAddress
    const now = Date.now()
    
    // 清理过期记录
    for (const [ip, data] of requests.entries()) {
      if (now - data.resetTime > windowMs) {
        requests.delete(ip)
      }
    }
    
    // 获取或创建请求记录
    let requestData = requests.get(key)
    if (!requestData) {
      requestData = {
        count: 0,
        resetTime: now
      }
      requests.set(key, requestData)
    }
    
    // 检查是否超过限制
    if (requestData.count >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: message,
        retryAfter: Math.ceil((requestData.resetTime + windowMs - now) / 1000)
      })
    }
    
    // 增加请求计数
    requestData.count++
    
    next()
  }
}

// 内容安全验证中间件
const contentSecurityMiddleware = (req, res, next) => {
  const { body } = req
  
  // 检查是否包含敏感词汇
  const sensitiveWords = ['政治', '暴力', '色情', '赌博', '毒品']
  const content = JSON.stringify(body).toLowerCase()
  
  for (const word of sensitiveWords) {
    if (content.includes(word)) {
      return res.status(400).json({
        success: false,
        message: '内容包含敏感词汇，请修改后重试'
      })
    }
  }
  
  next()
}

module.exports = {
  validationMiddleware,
  fileValidationMiddleware,
  paginationMiddleware,
  rateLimitMiddleware,
  contentSecurityMiddleware
}

// 默认导出验证中间件
module.exports.default = validationMiddleware
