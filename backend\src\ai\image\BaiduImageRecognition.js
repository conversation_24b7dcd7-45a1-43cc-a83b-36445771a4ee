const axios = require('axios')
const BaseImageRecognition = require('../base/BaseImageRecognition')

/**
 * 百度AI图像识别服务适配器
 */
class BaiduImageRecognition extends BaseImageRecognition {
  constructor (config) {
    super(config)
    this.accessToken = null
    this.tokenExpireTime = null
  }

  /**
   * 获取访问令牌
   * @returns {Promise<string>} 访问令牌
   */
  async getAccessToken () {
    // 检查token是否还有效
    if (this.accessToken && this.tokenExpireTime && Date.now() < this.tokenExpireTime) {
      return this.accessToken
    }

    try {
      const response = await axios.post(
        'https://aip.baidubce.com/oauth/2.0/token',
        null,
        {
          params: {
            grant_type: 'client_credentials',
            client_id: this.apiKey,
            client_secret: this.secretKey
          }
        }
      )

      this.accessToken = response.data.access_token
      this.tokenExpireTime = Date.now() + (response.data.expires_in - 300) * 1000 // 提前5分钟过期

      return this.accessToken
    } catch (error) {
      throw new Error(`获取百度AI访问令牌失败: ${error.message}`)
    }
  }

  /**
   * 识别图片中的食材
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeIngredients (image, options = {}) {
    try {
      const base64Image = this.preprocessImage(image)
      const accessToken = await this.getAccessToken()

      // 使用百度通用物体识别API
      const response = await axios.post(
        `https://aip.baidubce.com/rest/2.0/image-classify/v2/advanced_general?access_token=${accessToken}`,
        {
          image: base64Image,
          baike_num: 5
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )

      if (response.data.error_code) {
        throw new Error(`百度AI识别失败: ${response.data.error_msg}`)
      }

      return this.normalizeIngredientsResult(response.data)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 识别菜品
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeDish (image, options = {}) {
    try {
      const base64Image = this.preprocessImage(image)
      const accessToken = await this.getAccessToken()

      // 使用百度菜品识别API
      const response = await axios.post(
        `https://aip.baidubce.com/rest/2.0/image-classify/v2/dish?access_token=${accessToken}`,
        {
          image: base64Image,
          top_num: 5,
          filter_threshold: 0.7
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )

      if (response.data.error_code) {
        throw new Error(`百度AI菜品识别失败: ${response.data.error_msg}`)
      }

      return this.normalizeDishResult(response.data)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 通用物体识别
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeObject (image, options = {}) {
    try {
      const base64Image = this.preprocessImage(image)
      const accessToken = await this.getAccessToken()

      const response = await axios.post(
        `https://aip.baidubce.com/rest/2.0/image-classify/v2/advanced_general?access_token=${accessToken}`,
        {
          image: base64Image,
          baike_num: 5
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )

      if (response.data.error_code) {
        throw new Error(`百度AI物体识别失败: ${response.data.error_msg}`)
      }

      return this.normalizeObjectResult(response.data)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 标准化食材识别结果
   * @param {Object} rawResult - 百度AI原始结果
   * @returns {Object} 标准化结果
   */
  normalizeIngredientsResult (rawResult) {
    const ingredients = []
    
    if (rawResult.result && rawResult.result.length > 0) {
      for (const item of rawResult.result) {
        // 过滤出可能是食材的物品
        if (this.isFoodRelated(item.keyword)) {
          ingredients.push({
            name: item.keyword,
            confidence: item.score,
            category: this.categorizeIngredient(item.keyword),
            baike_info: item.baike_info || null
          })
        }
      }
    }

    return this.normalizeResult({
      ingredients,
      confidence: ingredients.length > 0 ? ingredients[0].confidence : 0,
      raw_result: rawResult
    })
  }

  /**
   * 标准化菜品识别结果
   * @param {Object} rawResult - 百度AI原始结果
   * @returns {Object} 标准化结果
   */
  normalizeDishResult (rawResult) {
    const dishes = []
    
    if (rawResult.result && rawResult.result.length > 0) {
      for (const item of rawResult.result) {
        dishes.push({
          name: item.name,
          confidence: item.probability,
          calorie: item.calorie || null,
          baike_info: item.baike_info || null
        })
      }
    }

    return this.normalizeResult({
      dishes,
      confidence: dishes.length > 0 ? dishes[0].confidence : 0,
      raw_result: rawResult
    })
  }

  /**
   * 标准化物体识别结果
   * @param {Object} rawResult - 百度AI原始结果
   * @returns {Object} 标准化结果
   */
  normalizeObjectResult (rawResult) {
    const objects = []
    
    if (rawResult.result && rawResult.result.length > 0) {
      for (const item of rawResult.result) {
        objects.push({
          name: item.keyword,
          confidence: item.score,
          root: item.root,
          baike_info: item.baike_info || null
        })
      }
    }

    return this.normalizeResult({
      objects,
      confidence: objects.length > 0 ? objects[0].confidence : 0,
      raw_result: rawResult
    })
  }

  /**
   * 判断是否为食物相关
   * @param {string} keyword - 关键词
   * @returns {boolean} 是否为食物相关
   */
  isFoodRelated (keyword) {
    const foodKeywords = [
      '蔬菜', '水果', '肉类', '海鲜', '鱼', '虾', '蟹', '贝类',
      '谷物', '豆类', '坚果', '调料', '香料', '油', '盐', '糖',
      '面', '米', '蛋', '奶', '菌类', '藻类'
    ]
    
    const nonFoodKeywords = [
      '人', '动物', '植物', '建筑', '车辆', '工具', '电器', '家具',
      '衣服', '鞋子', '包', '书', '手机', '电脑'
    ]
    
    // 检查是否包含食物关键词
    for (const foodWord of foodKeywords) {
      if (keyword.includes(foodWord)) {
        return true
      }
    }
    
    // 检查是否包含非食物关键词
    for (const nonFoodWord of nonFoodKeywords) {
      if (keyword.includes(nonFoodWord)) {
        return false
      }
    }
    
    // 默认返回true，让后续处理决定
    return true
  }

  /**
   * 食材分类
   * @param {string} ingredient - 食材名称
   * @returns {string} 分类
   */
  categorizeIngredient (ingredient) {
    const categories = {
      蔬菜: ['菜', '萝卜', '白菜', '菠菜', '芹菜', '韭菜', '豆角', '茄子', '番茄', '黄瓜', '冬瓜', '南瓜'],
      水果: ['苹果', '香蕉', '橙子', '葡萄', '草莓', '西瓜', '梨', '桃', '李', '杏'],
      肉类: ['猪肉', '牛肉', '羊肉', '鸡肉', '鸭肉', '鹅肉'],
      海鲜: ['鱼', '虾', '蟹', '贝', '海带', '紫菜'],
      谷物: ['米', '面', '麦', '玉米', '小米', '燕麦'],
      调料: ['盐', '糖', '醋', '酱油', '料酒', '胡椒', '花椒', '八角']
    }
    
    for (const [category, keywords] of Object.entries(categories)) {
      for (const keyword of keywords) {
        if (ingredient.includes(keyword)) {
          return category
        }
      }
    }
    
    return '其他'
  }

  /**
   * 计算调用成本
   * @param {Object} result - 识别结果
   * @returns {number} 成本（元）
   */
  calculateCost (result) {
    // 百度AI图像识别按次计费，约0.002元/次
    return 0.002
  }
}

module.exports = BaiduImageRecognition
