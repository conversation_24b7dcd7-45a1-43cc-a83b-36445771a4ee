/**
 * 文本生成服务基础抽象类
 */
class BaseTextGeneration {
  constructor (config) {
    this.config = config
    this.provider = config.provider
    this.apiKey = config.apiKey
    this.secretKey = config.secretKey
    this.baseUrl = config.baseUrl
    this.model = config.model || 'default'
  }

  /**
   * 生成菜谱
   * @param {Array} ingredients - 食材列表
   * @param {Object} preferences - 用户偏好
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成的菜谱
   */
  async generateRecipe (ingredients, preferences = {}, options = {}) {
    throw new Error('generateRecipe method must be implemented')
  }

  /**
   * 生成菜谱描述
   * @param {Object} recipe - 菜谱基本信息
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成的描述
   */
  async generateDescription (recipe, options = {}) {
    throw new Error('generateDescription method must be implemented')
  }

  /**
   * 生成制作小贴士
   * @param {Object} recipe - 菜谱信息
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成的小贴士
   */
  async generateTips (recipe, options = {}) {
    throw new Error('generateTips method must be implemented')
  }

  /**
   * 优化菜谱内容
   * @param {Object} recipe - 原始菜谱
   * @param {Object} options - 优化选项
   * @returns {Promise<Object>} 优化后的菜谱
   */
  async optimizeRecipe (recipe, options = {}) {
    throw new Error('optimizeRecipe method must be implemented')
  }

  /**
   * 构建菜谱生成提示词
   * @param {Array} ingredients - 食材列表
   * @param {Object} preferences - 用户偏好
   * @returns {string} 提示词
   */
  buildRecipePrompt (ingredients, preferences = {}) {
    const {
      cuisine_type = '不限',
      difficulty = '简单',
      cook_time = 30,
      servings = 2,
      dietary_restrictions = [],
      meal_type = '不限'
    } = preferences

    let prompt = `请根据以下食材生成一个详细的菜谱：\n\n`
    prompt += `食材：${ingredients.join('、')}\n`
    prompt += `菜系：${cuisine_type}\n`
    prompt += `难度：${difficulty}\n`
    prompt += `制作时间：约${cook_time}分钟\n`
    prompt += `份数：${servings}人份\n`
    prompt += `餐食类型：${meal_type}\n`
    
    if (dietary_restrictions.length > 0) {
      prompt += `饮食限制：${dietary_restrictions.join('、')}\n`
    }
    
    prompt += `\n请按照以下JSON格式返回菜谱：\n`
    prompt += `{\n`
    prompt += `  "name": "菜名",\n`
    prompt += `  "description": "菜品描述",\n`
    prompt += `  "ingredients": [\n`
    prompt += `    {"name": "食材名", "amount": "用量", "unit": "单位"}\n`
    prompt += `  ],\n`
    prompt += `  "steps": [\n`
    prompt += `    {"step": 1, "description": "步骤描述", "time": "所需时间", "tips": "小贴士"}\n`
    prompt += `  ],\n`
    prompt += `  "tips": "制作小贴士",\n`
    prompt += `  "nutrition": {\n`
    prompt += `    "calories": "卡路里",\n`
    prompt += `    "protein": "蛋白质",\n`
    prompt += `    "carbs": "碳水化合物",\n`
    prompt += `    "fat": "脂肪"\n`
    prompt += `  },\n`
    prompt += `  "tags": ["标签1", "标签2"]\n`
    prompt += `}`

    return prompt
  }

  /**
   * 解析AI返回的菜谱内容
   * @param {string} content - AI返回的文本内容
   * @returns {Object} 解析后的菜谱对象
   */
  parseRecipeContent (content) {
    try {
      // 尝试提取JSON内容
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const jsonStr = jsonMatch[0]
        return JSON.parse(jsonStr)
      }
      
      // 如果没有找到JSON，尝试解析文本格式
      return this.parseTextRecipe(content)
    } catch (error) {
      console.error('解析菜谱内容失败:', error)
      throw new Error('无法解析AI返回的菜谱内容')
    }
  }

  /**
   * 解析文本格式的菜谱
   * @param {string} content - 文本内容
   * @returns {Object} 菜谱对象
   */
  parseTextRecipe (content) {
    // 简单的文本解析逻辑，子类可以重写
    return {
      name: '未知菜名',
      description: content.substring(0, 100),
      ingredients: [],
      steps: [{ step: 1, description: content, time: '', tips: '' }],
      tips: '',
      nutrition: {},
      tags: []
    }
  }

  /**
   * 标准化生成结果
   * @param {Object} rawResult - 原始生成结果
   * @param {number} tokensUsed - 使用的token数量
   * @returns {Object} 标准化结果
   */
  normalizeResult (rawResult, tokensUsed = 0) {
    return {
      success: true,
      provider: this.provider,
      model: this.model,
      timestamp: new Date().toISOString(),
      tokens_used: tokensUsed,
      data: rawResult
    }
  }

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @returns {Object} 标准化错误结果
   */
  handleError (error) {
    console.error(`${this.provider} 文本生成服务错误:`, error)
    
    return {
      success: false,
      provider: this.provider,
      model: this.model,
      timestamp: new Date().toISOString(),
      error: {
        message: error.message,
        code: error.code || 'UNKNOWN_ERROR',
        details: error.response?.data || null
      }
    }
  }

  /**
   * 计算调用成本
   * @param {number} tokensUsed - 使用的token数量
   * @returns {number} 成本（元）
   */
  calculateCost (tokensUsed) {
    // 子类实现具体的成本计算逻辑
    return 0
  }

  /**
   * 验证配置
   * @returns {boolean} 配置是否有效
   */
  validateConfig () {
    if (!this.apiKey) {
      throw new Error(`${this.provider} API Key is required`)
    }
    return true
  }

  /**
   * 获取服务状态
   * @returns {Promise<Object>} 服务状态
   */
  async getStatus () {
    try {
      return {
        provider: this.provider,
        model: this.model,
        status: 'available',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        provider: this.provider,
        model: this.model,
        status: 'unavailable',
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }
}

module.exports = BaseTextGeneration
