const redis = require('redis')
require('dotenv').config()

const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null
}

// 创建Redis客户端
const client = redis.createClient({
  socket: {
    host: redisConfig.host,
    port: redisConfig.port
  },
  password: redisConfig.password,
  database: redisConfig.db
})

// 错误处理
client.on('error', (err) => {
  console.error('Redis Client Error:', err)
})

client.on('connect', () => {
  console.log('✅ Redis connected successfully')
})

client.on('ready', () => {
  console.log('✅ Redis client ready')
})

client.on('end', () => {
  console.log('❌ Redis connection ended')
})

// 连接Redis
const connectRedis = async () => {
  try {
    await client.connect()
    console.log('✅ Redis connection established')
  } catch (error) {
    console.error('❌ Redis connection failed:', error)
  }
}

// Redis工具函数
const redisUtils = {
  // 设置缓存
  async set (key, value, expireTime = 3600) {
    try {
      const serializedValue = JSON.stringify(value)
      await client.setEx(key, expireTime, serializedValue)
      return true
    } catch (error) {
      console.error('Redis set error:', error)
      return false
    }
  },

  // 获取缓存
  async get (key) {
    try {
      const value = await client.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Redis get error:', error)
      return null
    }
  },

  // 删除缓存
  async del (key) {
    try {
      await client.del(key)
      return true
    } catch (error) {
      console.error('Redis del error:', error)
      return false
    }
  },

  // 检查key是否存在
  async exists (key) {
    try {
      const result = await client.exists(key)
      return result === 1
    } catch (error) {
      console.error('Redis exists error:', error)
      return false
    }
  },

  // 设置过期时间
  async expire (key, seconds) {
    try {
      await client.expire(key, seconds)
      return true
    } catch (error) {
      console.error('Redis expire error:', error)
      return false
    }
  }
}

module.exports = {
  client,
  connectRedis,
  redisUtils
}
