'use strict'

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('recognition_records', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      user_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '用户ID',
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      image_url: {
        type: Sequelize.STRING(200),
        allowNull: false,
        comment: '上传的图片URL'
      },
      recognition_result: {
        type: Sequelize.JSON,
        allowNull: false,
        comment: '识别结果'
      },
      ai_provider: {
        type: Sequelize.STRING(20),
        allowNull: false,
        comment: 'AI服务提供商'
      },
      ai_model: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'AI模型名称'
      },
      processing_time: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '处理时间(毫秒)'
      },
      confidence_score: {
        type: Sequelize.DECIMAL(5, 4),
        allowNull: true,
        comment: '置信度分数'
      },
      status: {
        type: Sequelize.ENUM('pending', 'success', 'failed'),
        defaultValue: 'pending',
        comment: '识别状态'
      },
      error_message: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '错误信息'
      },
      cost: {
        type: Sequelize.DECIMAL(10, 6),
        allowNull: true,
        comment: '调用成本'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    })

    // 添加索引
    await queryInterface.addIndex('recognition_records', ['user_id'])
    await queryInterface.addIndex('recognition_records', ['ai_provider'])
    await queryInterface.addIndex('recognition_records', ['status'])
    await queryInterface.addIndex('recognition_records', ['created_at'])
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('recognition_records')
  }
}
