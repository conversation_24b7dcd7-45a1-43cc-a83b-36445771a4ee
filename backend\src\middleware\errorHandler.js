const fs = require('fs')
const path = require('path')

// 自定义错误类
class AppError extends Error {
  constructor (message, statusCode = 500, code = null) {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.isOperational = true
    
    Error.captureStackTrace(this, this.constructor)
  }
}

// 异步错误捕获包装器
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// 全局错误处理中间件
const globalErrorHandler = (err, req, res, next) => {
  let error = { ...err }
  error.message = err.message
  
  // 记录错误日志
  logError(err, req)
  
  // Sequelize 验证错误
  if (err.name === 'SequelizeValidationError') {
    const message = err.errors.map(e => e.message).join(', ')
    error = new AppError(message, 400, 'VALIDATION_ERROR')
  }
  
  // Sequelize 唯一约束错误
  if (err.name === 'SequelizeUniqueConstraintError') {
    const field = err.errors[0]?.path || 'field'
    const message = `${field} 已存在`
    error = new AppError(message, 400, 'DUPLICATE_ERROR')
  }
  
  // Sequelize 外键约束错误
  if (err.name === 'SequelizeForeignKeyConstraintError') {
    const message = '关联数据不存在'
    error = new AppError(message, 400, 'FOREIGN_KEY_ERROR')
  }
  
  // JWT 错误
  if (err.name === 'JsonWebTokenError') {
    const message = 'Token无效'
    error = new AppError(message, 401, 'INVALID_TOKEN')
  }
  
  if (err.name === 'TokenExpiredError') {
    const message = 'Token已过期'
    error = new AppError(message, 401, 'TOKEN_EXPIRED')
  }
  
  // Multer 文件上传错误
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = '文件大小超出限制'
    error = new AppError(message, 400, 'FILE_TOO_LARGE')
  }
  
  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = '不支持的文件字段'
    error = new AppError(message, 400, 'INVALID_FILE_FIELD')
  }
  
  // 发送错误响应
  sendErrorResponse(error, req, res)
}

// 发送错误响应
const sendErrorResponse = (err, req, res) => {
  const statusCode = err.statusCode || 500
  const isDev = process.env.NODE_ENV === 'development'
  
  // 生产环境不暴露敏感错误信息
  if (!isDev && statusCode === 500) {
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    })
  }
  
  const errorResponse = {
    success: false,
    message: err.message || '未知错误',
    code: err.code || 'UNKNOWN_ERROR'
  }
  
  // 开发环境返回详细错误信息
  if (isDev) {
    errorResponse.stack = err.stack
    errorResponse.details = {
      statusCode,
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method
    }
  }
  
  res.status(statusCode).json(errorResponse)
}

// 记录错误日志
const logError = (err, req) => {
  const logData = {
    timestamp: new Date().toISOString(),
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name,
      code: err.code
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip
    },
    user: req.user || null
  }
  
  // 写入错误日志文件
  const logDir = path.join(__dirname, '../../logs')
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true })
  }
  
  const logFile = path.join(logDir, `error-${new Date().toISOString().split('T')[0]}.log`)
  const logEntry = JSON.stringify(logData) + '\n'
  
  fs.appendFileSync(logFile, logEntry)
  
  // 控制台输出
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    user: req.user?.userId || 'anonymous'
  })
}

// 404 处理中间件
const notFoundHandler = (req, res, next) => {
  const error = new AppError(`路径 ${req.originalUrl} 不存在`, 404, 'NOT_FOUND')
  next(error)
}

// 未捕获异常处理
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err)
  process.exit(1)
})

// 未处理的Promise拒绝
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Rejection:', err)
  process.exit(1)
})

module.exports = {
  AppError,
  asyncHandler,
  globalErrorHandler,
  notFoundHandler,
  sendErrorResponse,
  logError
}
