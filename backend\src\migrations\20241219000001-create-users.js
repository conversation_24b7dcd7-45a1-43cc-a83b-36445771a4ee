'use strict'

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('users', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      openid: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
        comment: '微信小程序用户唯一标识'
      },
      nickname: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '用户昵称'
      },
      avatar: {
        type: Sequelize.STRING(200),
        allowNull: true,
        comment: '用户头像URL'
      },
      preferences: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '用户偏好设置'
      },
      health_profile: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '健康档案'
      },
      status: {
        type: Sequelize.ENUM('active', 'inactive', 'banned'),
        defaultValue: 'active',
        comment: '用户状态'
      },
      last_login_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '最后登录时间'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    })

    // 添加索引
    await queryInterface.addIndex('users', ['openid'], {
      unique: true,
      name: 'users_openid_unique'
    })
    await queryInterface.addIndex('users', ['status'])
    await queryInterface.addIndex('users', ['created_at'])
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('users')
  }
}
