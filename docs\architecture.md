# 系统架构设计

## 整体架构概览

### 架构图

```mermaid
graph TB
    subgraph "客户端层"
        MP[微信小程序]
        MP --> |HTTPS| Gateway
    end
    
    subgraph "网关层"
        Gateway[API网关]
        Gateway --> Auth[身份认证]
        Gateway --> RateLimit[限流控制]
        Gateway --> Log[日志记录]
    end
    
    subgraph "应用层"
        API[Node.js API服务]
        API --> UserCtrl[用户控制器]
        API --> AICtrl[AI控制器]
        API --> RecipeCtrl[菜谱控制器]
    end
    
    subgraph "业务逻辑层"
        UserService[用户服务]
        AIService[AI服务]
        RecipeService[菜谱服务]
        FileService[文件服务]
    end
    
    subgraph "数据访问层"
        UserModel[用户模型]
        RecipeModel[菜谱模型]
        AIModel[AI记录模型]
    end
    
    subgraph "数据存储层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        COS[腾讯云COS]
    end
    
    subgraph "外部服务层"
        BaiduAI[百度AI]
        TencentAI[腾讯云AI]
        AliyunAI[阿里云AI]
        QwenAI[通义千问]
        SparkAI[讯飞星火]
        WenxinAI[百度文心]
    end
    
    UserCtrl --> UserService
    AICtrl --> AIService
    RecipeCtrl --> RecipeService
    
    UserService --> UserModel
    AIService --> AIModel
    RecipeService --> RecipeModel
    
    UserModel --> MySQL
    RecipeModel --> MySQL
    AIModel --> MySQL
    
    AIService --> Redis
    UserService --> Redis
    
    FileService --> COS
    
    AIService --> BaiduAI
    AIService --> TencentAI
    AIService --> AliyunAI
    AIService --> QwenAI
    AIService --> SparkAI
    AIService --> WenxinAI
```

## 技术架构

### 前端架构（微信小程序）

```mermaid
graph TB
    subgraph "页面层"
        IndexPage[首页]
        CameraPage[拍照页]
        ResultPage[结果页]
        RecipePage[菜谱页]
        ProfilePage[个人中心]
    end
    
    subgraph "组件层"
        RecipeCard[菜谱卡片]
        ImageUpload[图片上传]
        LoadingSpinner[加载组件]
        EmptyState[空状态]
    end
    
    subgraph "工具层"
        APIUtil[API工具]
        AuthUtil[认证工具]
        StorageUtil[存储工具]
        ImageUtil[图片工具]
    end
    
    subgraph "状态管理"
        GlobalData[全局数据]
        UserState[用户状态]
        CacheState[缓存状态]
    end
    
    IndexPage --> RecipeCard
    CameraPage --> ImageUpload
    ResultPage --> LoadingSpinner
    
    RecipeCard --> APIUtil
    ImageUpload --> ImageUtil
    
    APIUtil --> AuthUtil
    AuthUtil --> StorageUtil
    
    GlobalData --> UserState
    UserState --> CacheState
```

### 后端架构（Node.js）

```mermaid
graph TB
    subgraph "路由层"
        AuthRoute[认证路由]
        UserRoute[用户路由]
        AIRoute[AI路由]
        RecipeRoute[菜谱路由]
    end
    
    subgraph "中间件层"
        AuthMiddleware[身份认证]
        ValidationMiddleware[参数验证]
        ErrorMiddleware[错误处理]
        LogMiddleware[日志记录]
    end
    
    subgraph "控制器层"
        AuthController[认证控制器]
        UserController[用户控制器]
        AIController[AI控制器]
        RecipeController[菜谱控制器]
    end
    
    subgraph "服务层"
        AuthService[认证服务]
        UserService[用户服务]
        AIServiceFactory[AI服务工厂]
        RecipeService[菜谱服务]
        FileService[文件服务]
    end
    
    subgraph "数据层"
        Models[Sequelize模型]
        Redis[Redis客户端]
        Database[数据库连接]
    end
    
    AuthRoute --> AuthMiddleware
    UserRoute --> ValidationMiddleware
    AIRoute --> ErrorMiddleware
    RecipeRoute --> LogMiddleware
    
    AuthMiddleware --> AuthController
    ValidationMiddleware --> UserController
    ErrorMiddleware --> AIController
    LogMiddleware --> RecipeController
    
    AuthController --> AuthService
    UserController --> UserService
    AIController --> AIServiceFactory
    RecipeController --> RecipeService
    
    AuthService --> Models
    UserService --> Redis
    AIServiceFactory --> Database
    RecipeService --> Models
```

## 核心模块设计

### 1. AI服务适配器模式

```mermaid
classDiagram
    class BaseImageRecognition {
        <<abstract>>
        +recognizeIngredients()
        +recognizeDish()
        +recognizeObject()
        +preprocessImage()
        +normalizeResult()
        +calculateCost()
    }
    
    class BaseTextGeneration {
        <<abstract>>
        +generateRecipe()
        +generateDescription()
        +generateTips()
        +optimizeRecipe()
        +buildPrompt()
        +parseContent()
    }
    
    class AIServiceFactory {
        +getImageService()
        +getTextService()
        +checkServicesStatus()
        +getAvailableServices()
    }
    
    class BaiduImageRecognition {
        +recognizeIngredients()
        +getAccessToken()
        +callAPI()
    }
    
    class QwenTextGeneration {
        +generateRecipe()
        +callAPI()
        +parseRecipeContent()
    }
    
    BaseImageRecognition <|-- BaiduImageRecognition
    BaseImageRecognition <|-- TencentImageRecognition
    BaseImageRecognition <|-- AliyunImageRecognition
    
    BaseTextGeneration <|-- QwenTextGeneration
    BaseTextGeneration <|-- SparkTextGeneration
    BaseTextGeneration <|-- WenxinTextGeneration
    
    AIServiceFactory --> BaseImageRecognition
    AIServiceFactory --> BaseTextGeneration
```

### 2. 数据模型关系

```mermaid
erDiagram
    User ||--o{ UserFavorite : has
    User ||--o{ RecognitionRecord : creates
    Recipe ||--o{ UserFavorite : favorited_by
    Recipe ||--o{ Ingredient : contains
    RecognitionRecord ||--o{ AiUsageStats : generates
    
    User {
        int id PK
        string openid UK
        string nickname
        string avatar_url
        json preferences
        json health_profile
        datetime created_at
        datetime updated_at
    }
    
    Recipe {
        int id PK
        string name
        text description
        json ingredients
        json steps
        json nutrition
        int difficulty
        int cook_time
        int prep_time
        int servings
        float rating
        int view_count
        datetime created_at
    }
    
    Ingredient {
        int id PK
        string name UK
        string category
        json aliases
        json nutrition_info
        json storage_info
        json seasonal_info
        datetime created_at
    }
    
    RecognitionRecord {
        int id PK
        int user_id FK
        string image_url
        string ai_provider
        string ai_model
        json recognition_result
        float confidence_score
        int processing_time
        float cost
        enum status
        text error_message
        datetime created_at
    }
    
    UserFavorite {
        int id PK
        int user_id FK
        int recipe_id FK
        text notes
        json custom_tags
        datetime created_at
    }
    
    AiUsageStats {
        int id PK
        date stat_date
        int stat_hour
        string service_type
        string provider
        string model_name
        int total_calls
        int success_calls
        int failed_calls
        bigint total_tokens
        decimal total_cost
        int avg_response_time
        datetime created_at
    }
```

## 数据流设计

### 图像识别流程

```mermaid
sequenceDiagram
    participant MP as 微信小程序
    participant API as API服务
    participant AI as AI服务工厂
    participant Provider as AI厂商
    participant DB as 数据库
    participant Cache as Redis缓存
    
    MP->>API: 上传图片
    API->>DB: 创建识别记录
    API->>AI: 获取图像识别服务
    AI->>Provider: 调用识别API
    Provider-->>AI: 返回识别结果
    AI-->>API: 标准化结果
    API->>DB: 更新识别记录
    API->>Cache: 缓存结果
    API-->>MP: 返回识别结果
```

### 菜谱生成流程

```mermaid
sequenceDiagram
    participant MP as 微信小程序
    participant API as API服务
    participant AI as AI服务工厂
    participant LLM as 大语言模型
    participant DB as 数据库
    participant Cache as Redis缓存
    
    MP->>API: 请求生成菜谱
    API->>Cache: 检查缓存
    alt 缓存命中
        Cache-->>API: 返回缓存结果
    else 缓存未命中
        API->>AI: 获取文本生成服务
        AI->>LLM: 调用生成API
        LLM-->>AI: 返回生成内容
        AI-->>API: 解析菜谱内容
        API->>DB: 保存菜谱
        API->>Cache: 缓存结果
    end
    API-->>MP: 返回菜谱数据
```

## 安全架构

### 认证授权流程

```mermaid
sequenceDiagram
    participant MP as 微信小程序
    participant WX as 微信服务器
    participant API as API服务
    participant DB as 数据库
    participant JWT as JWT服务
    
    MP->>WX: wx.login()
    WX-->>MP: 返回code
    MP->>API: 发送code
    API->>WX: 验证code
    WX-->>API: 返回openid
    API->>DB: 查询/创建用户
    API->>JWT: 生成JWT token
    JWT-->>API: 返回token
    API-->>MP: 返回用户信息和token
    
    Note over MP,API: 后续请求携带token
    MP->>API: 请求(带token)
    API->>JWT: 验证token
    JWT-->>API: 验证结果
    API-->>MP: 返回数据
```

## 性能优化策略

### 缓存策略

1. **Redis缓存层级**
   - L1: 热点数据缓存（用户信息、推荐菜谱）
   - L2: 计算结果缓存（AI识别结果、生成的菜谱）
   - L3: 静态资源缓存（图片URL、配置信息）

2. **缓存更新策略**
   - 写入时更新（Write-through）
   - 延迟写入（Write-behind）
   - 定时刷新（Time-based refresh）

### 数据库优化

1. **索引策略**
   - 主键索引：所有表的id字段
   - 唯一索引：用户openid、食材名称
   - 复合索引：查询条件组合
   - 全文索引：菜谱搜索

2. **分页优化**
   - 游标分页替代偏移分页
   - 预加载关联数据
   - 分页缓存策略

## 监控和日志

### 监控指标

1. **业务指标**
   - 用户活跃度
   - 识别成功率
   - 菜谱生成质量
   - 用户满意度

2. **技术指标**
   - API响应时间
   - 数据库查询性能
   - 缓存命中率
   - 错误率统计

3. **成本指标**
   - AI服务调用成本
   - 存储成本
   - 带宽成本
   - 服务器成本

### 日志策略

1. **日志级别**
   - ERROR: 系统错误、业务异常
   - WARN: 性能警告、降级处理
   - INFO: 业务流程、关键操作
   - DEBUG: 调试信息、详细日志

2. **日志格式**
   - 结构化日志（JSON格式）
   - 统一时间戳格式
   - 请求链路追踪ID
   - 用户标识和操作类型

## 扩展性设计

### 水平扩展

1. **无状态设计**
   - API服务无状态
   - 会话信息存储在Redis
   - 文件存储使用云服务

2. **负载均衡**
   - API服务多实例部署
   - 数据库读写分离
   - CDN加速静态资源

### 垂直扩展

1. **模块化设计**
   - 微服务架构准备
   - 服务边界清晰
   - 接口标准化

2. **插件化扩展**
   - AI服务适配器模式
   - 支付方式插件化
   - 第三方服务集成
