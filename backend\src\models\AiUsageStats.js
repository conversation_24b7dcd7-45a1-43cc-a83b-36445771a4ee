const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const AiUsageStats = sequelize.define('AiUsageStats', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  service_type: {
    type: DataTypes.ENUM('image', 'text'),
    allowNull: false,
    comment: '服务类型'
  },
  provider: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: 'AI服务提供商'
  },
  model_name: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '模型名称'
  },
  tokens_used: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '使用的token数量'
  },
  requests_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '请求次数'
  },
  success_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '成功次数'
  },
  error_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '错误次数'
  },
  cost: {
    type: DataTypes.DECIMAL(10, 4),
    allowNull: false,
    defaultValue: 0,
    comment: '成本'
  },
  avg_response_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '平均响应时间(毫秒)'
  },
  date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '统计日期'
  },
  hour: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '统计小时(0-23)'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'ai_usage_stats',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['service_type', 'provider', 'date', 'hour']
    },
    {
      fields: ['service_type']
    },
    {
      fields: ['provider']
    },
    {
      fields: ['date']
    }
  ]
})

// 类方法
AiUsageStats.recordUsage = async function (data) {
  const {
    serviceType,
    provider,
    modelName,
    tokensUsed = 0,
    cost = 0,
    responseTime = null,
    success = true
  } = data

  const now = new Date()
  const date = now.toISOString().split('T')[0]
  const hour = now.getHours()

  const [stats, created] = await this.findOrCreate({
    where: {
      service_type: serviceType,
      provider: provider,
      date: date,
      hour: hour
    },
    defaults: {
      service_type: serviceType,
      provider: provider,
      model_name: modelName,
      tokens_used: tokensUsed,
      requests_count: 1,
      success_count: success ? 1 : 0,
      error_count: success ? 0 : 1,
      cost: cost,
      avg_response_time: responseTime,
      date: date,
      hour: hour
    }
  })

  if (!created) {
    // 更新现有记录
    const newRequestsCount = stats.requests_count + 1
    const newSuccessCount = stats.success_count + (success ? 1 : 0)
    const newErrorCount = stats.error_count + (success ? 0 : 1)
    const newTokensUsed = stats.tokens_used + tokensUsed
    const newCost = parseFloat(stats.cost) + parseFloat(cost)
    
    let newAvgResponseTime = stats.avg_response_time
    if (responseTime && stats.avg_response_time) {
      newAvgResponseTime = Math.round(
        (stats.avg_response_time * (newRequestsCount - 1) + responseTime) / newRequestsCount
      )
    } else if (responseTime) {
      newAvgResponseTime = responseTime
    }

    await stats.update({
      tokens_used: newTokensUsed,
      requests_count: newRequestsCount,
      success_count: newSuccessCount,
      error_count: newErrorCount,
      cost: newCost,
      avg_response_time: newAvgResponseTime
    })
  }

  return stats
}

AiUsageStats.getDailyStats = function (startDate, endDate) {
  return this.findAll({
    attributes: [
      'date',
      'service_type',
      'provider',
      [sequelize.fn('SUM', sequelize.col('requests_count')), 'total_requests'],
      [sequelize.fn('SUM', sequelize.col('success_count')), 'total_success'],
      [sequelize.fn('SUM', sequelize.col('error_count')), 'total_errors'],
      [sequelize.fn('SUM', sequelize.col('tokens_used')), 'total_tokens'],
      [sequelize.fn('SUM', sequelize.col('cost')), 'total_cost'],
      [sequelize.fn('AVG', sequelize.col('avg_response_time')), 'avg_response_time']
    ],
    where: {
      date: {
        [sequelize.Op.between]: [startDate, endDate]
      }
    },
    group: ['date', 'service_type', 'provider'],
    order: [['date', 'DESC']]
  })
}

AiUsageStats.getProviderComparison = function (date) {
  return this.findAll({
    attributes: [
      'provider',
      'service_type',
      [sequelize.fn('SUM', sequelize.col('requests_count')), 'total_requests'],
      [sequelize.fn('SUM', sequelize.col('cost')), 'total_cost'],
      [sequelize.fn('AVG', sequelize.col('avg_response_time')), 'avg_response_time'],
      [sequelize.literal('(SUM(success_count) / SUM(requests_count) * 100)'), 'success_rate']
    ],
    where: { date: date },
    group: ['provider', 'service_type']
  })
}

module.exports = AiUsageStats
