const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const morgan = require('morgan')
const compression = require('compression')
require('dotenv').config()

const app = express()
const PORT = process.env.PORT || 3000

// 中间件配置
app.use(helmet()) // 安全头
app.use(cors()) // 跨域
app.use(compression()) // 压缩
app.use(morgan('combined')) // 日志
app.use(express.json({ limit: '10mb' })) // JSON解析
app.use(express.urlencoded({ extended: true, limit: '10mb' })) // URL编码解析

// 静态文件服务
app.use('/uploads', express.static('uploads'))

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  })
})

// API路由
app.use('/api/auth', require('./routes/auth'))
app.use('/api/users', require('./routes/users'))
app.use('/api/ingredients', require('./routes/ingredients'))
app.use('/api/recipes', require('./routes/recipes'))
app.use('/api/recognition', require('./routes/recognition'))
app.use('/api/ai', require('./routes/ai'))

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found'
  })
})

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('Error:', err)
  
  // 开发环境返回详细错误信息
  const isDev = process.env.NODE_ENV === 'development'
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal Server Error',
    ...(isDev && { stack: err.stack })
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Server is running on port ${PORT}`)
  console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`)
  console.log(`🔗 Health check: http://localhost:${PORT}/health`)
})

module.exports = app
