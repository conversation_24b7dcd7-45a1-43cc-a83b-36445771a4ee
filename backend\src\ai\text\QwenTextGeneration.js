const axios = require('axios')
const BaseTextGeneration = require('../base/BaseTextGeneration')

/**
 * 通义千问文本生成服务适配器
 */
class QwenTextGeneration extends BaseTextGeneration {
  constructor (config) {
    super(config)
    this.baseUrl = config.baseUrl || 'https://dashscope.aliyuncs.com'
    this.model = config.model || 'qwen-turbo'
  }

  /**
   * 生成菜谱
   * @param {Array} ingredients - 食材列表
   * @param {Object} preferences - 用户偏好
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成的菜谱
   */
  async generateRecipe (ingredients, preferences = {}, options = {}) {
    try {
      const prompt = this.buildRecipePrompt(ingredients, preferences)
      
      const response = await this.callAPI({
        model: this.model,
        input: {
          messages: [
            {
              role: 'system',
              content: '你是一个专业的厨师和营养师，擅长根据食材创造美味健康的菜谱。请严格按照JSON格式返回菜谱信息。'
            },
            {
              role: 'user',
              content: prompt
            }
          ]
        },
        parameters: {
          temperature: 0.7,
          max_tokens: 2000,
          top_p: 0.9
        }
      })

      const content = response.output.choices[0].message.content
      const recipe = this.parseRecipeContent(content)
      
      return this.normalizeResult(recipe, response.usage?.total_tokens || 0)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 生成菜谱描述
   * @param {Object} recipe - 菜谱基本信息
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成的描述
   */
  async generateDescription (recipe, options = {}) {
    try {
      const prompt = `请为以下菜谱生成一段吸引人的描述（100字以内）：
菜名：${recipe.name}
主要食材：${recipe.ingredients?.map(i => i.name).join('、') || '未知'}
制作难度：${recipe.difficulty || '中等'}
请突出菜品的特色、口感和营养价值。`

      const response = await this.callAPI({
        model: this.model,
        input: {
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        },
        parameters: {
          temperature: 0.8,
          max_tokens: 200
        }
      })

      const description = response.output.choices[0].message.content.trim()
      
      return this.normalizeResult({ description }, response.usage?.total_tokens || 0)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 生成制作小贴士
   * @param {Object} recipe - 菜谱信息
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成的小贴士
   */
  async generateTips (recipe, options = {}) {
    try {
      const prompt = `请为以下菜谱生成3-5个实用的制作小贴士：
菜名：${recipe.name}
主要食材：${recipe.ingredients?.map(i => i.name).join('、') || '未知'}
制作步骤：${recipe.steps?.length || 0}步

请提供具体的技巧建议，比如火候控制、调料搭配、食材处理等。`

      const response = await this.callAPI({
        model: this.model,
        input: {
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        },
        parameters: {
          temperature: 0.7,
          max_tokens: 300
        }
      })

      const tips = response.output.choices[0].message.content.trim()
      
      return this.normalizeResult({ tips }, response.usage?.total_tokens || 0)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 优化菜谱内容
   * @param {Object} recipe - 原始菜谱
   * @param {Object} options - 优化选项
   * @returns {Promise<Object>} 优化后的菜谱
   */
  async optimizeRecipe (recipe, options = {}) {
    try {
      const prompt = `请优化以下菜谱，使其更加详细和专业：
${JSON.stringify(recipe, null, 2)}

优化要求：
1. 完善制作步骤的描述
2. 添加具体的用量和时间
3. 补充营养信息
4. 优化语言表达

请返回优化后的完整菜谱JSON。`

      const response = await this.callAPI({
        model: this.model,
        input: {
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        },
        parameters: {
          temperature: 0.6,
          max_tokens: 2500
        }
      })

      const content = response.output.choices[0].message.content
      const optimizedRecipe = this.parseRecipeContent(content)
      
      return this.normalizeResult(optimizedRecipe, response.usage?.total_tokens || 0)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 调用通义千问API
   * @param {Object} payload - 请求载荷
   * @returns {Promise<Object>} API响应
   */
  async callAPI (payload) {
    const response = await axios.post(
      `${this.baseUrl}/api/v1/services/aigc/text-generation/generation`,
      payload,
      {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'X-DashScope-SSE': 'disable'
        }
      }
    )

    if (response.data.code) {
      throw new Error(`通义千问API错误: ${response.data.message}`)
    }

    return response.data
  }

  /**
   * 解析AI返回的菜谱内容
   * @param {string} content - AI返回的文本内容
   * @returns {Object} 解析后的菜谱对象
   */
  parseRecipeContent (content) {
    try {
      // 尝试提取JSON内容
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const jsonStr = jsonMatch[0]
        const parsed = JSON.parse(jsonStr)
        
        // 验证必要字段
        return {
          name: parsed.name || '未知菜名',
          description: parsed.description || '',
          ingredients: Array.isArray(parsed.ingredients) ? parsed.ingredients : [],
          steps: Array.isArray(parsed.steps) ? parsed.steps : [],
          tips: parsed.tips || '',
          nutrition: parsed.nutrition || {},
          tags: Array.isArray(parsed.tags) ? parsed.tags : [],
          difficulty: parsed.difficulty || 1,
          cook_time: parsed.cook_time || 30,
          prep_time: parsed.prep_time || 15,
          servings: parsed.servings || 2
        }
      }
      
      // 如果没有找到JSON，返回基础结构
      return {
        name: '生成的菜谱',
        description: content.substring(0, 200),
        ingredients: [],
        steps: [{ step: 1, description: content, time: '', tips: '' }],
        tips: '',
        nutrition: {},
        tags: [],
        difficulty: 1,
        cook_time: 30,
        prep_time: 15,
        servings: 2
      }
    } catch (error) {
      console.error('解析通义千问菜谱内容失败:', error)
      throw new Error('无法解析AI返回的菜谱内容')
    }
  }

  /**
   * 计算调用成本
   * @param {number} tokensUsed - 使用的token数量
   * @returns {number} 成本（元）
   */
  calculateCost (tokensUsed) {
    // 通义千问按token计费，约0.002元/1000tokens
    return (tokensUsed / 1000) * 0.002
  }

  /**
   * 获取服务状态
   * @returns {Promise<Object>} 服务状态
   */
  async getStatus () {
    try {
      const response = await this.callAPI({
        model: this.model,
        input: {
          messages: [
            {
              role: 'user',
              content: '你好'
            }
          ]
        },
        parameters: {
          max_tokens: 10
        }
      })
      
      return {
        provider: this.provider,
        model: this.model,
        status: 'available',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        provider: this.provider,
        model: this.model,
        status: 'unavailable',
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }
}

module.exports = QwenTextGeneration
