const express = require('express')
const router = express.Router()
const authController = require('../controllers/authController')
const authMiddleware = require('../middleware/auth')
const validationMiddleware = require('../middleware/validation')
const { body } = require('express-validator')

// 微信登录验证规则
const wxLoginValidation = [
  body('code')
    .notEmpty()
    .withMessage('登录凭证不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('登录凭证格式不正确'),
  body('userInfo')
    .optional()
    .isObject()
    .withMessage('用户信息必须是对象格式'),
  body('userInfo.nickName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('昵称长度必须在1-50个字符之间'),
  body('userInfo.avatarUrl')
    .optional()
    .isURL()
    .withMessage('头像必须是有效的URL')
]

// 微信小程序登录
router.post('/wx-login',
  wxLoginValidation,
  validationMiddleware,
  authController.wxLogin
)

// 刷新token（需要认证）
router.post('/refresh-token',
  authMiddleware,
  authController.refreshToken
)

// 登出（需要认证）
router.post('/logout',
  authMiddleware,
  authController.logout
)

// 验证token状态
router.get('/verify',
  authMiddleware,
  (req, res) => {
    res.json({
      success: true,
      message: 'Token有效',
      data: {
        userId: req.user.userId
      }
    })
  }
)

module.exports = router
