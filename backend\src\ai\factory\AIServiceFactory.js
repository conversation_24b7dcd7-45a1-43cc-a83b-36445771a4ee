const path = require('path')

/**
 * AI服务工厂类
 * 负责创建和管理各种AI服务实例
 */
class AIServiceFactory {
  constructor () {
    this.imageProviders = new Map()
    this.textProviders = new Map()
    this.configs = this.loadConfigs()
    this.initializeProviders()
  }

  /**
   * 加载AI服务配置
   * @returns {Object} 配置对象
   */
  loadConfigs () {
    return {
      image: {
        baidu: {
          provider: 'baidu',
          apiKey: process.env.BAIDU_API_KEY,
          secretKey: process.env.BAIDU_SECRET_KEY,
          baseUrl: 'https://aip.baidubce.com',
          enabled: !!process.env.BAIDU_API_KEY
        },
        tencent: {
          provider: 'tencent',
          apiKey: process.env.TENCENT_SECRET_ID,
          secretKey: process.env.TENCENT_SECRET_KEY,
          region: process.env.TENCENT_REGION || 'ap-beijing',
          enabled: !!process.env.TENCENT_SECRET_ID
        },
        aliyun: {
          provider: 'aliyun',
          apiKey: process.env.ALIYUN_ACCESS_KEY_ID,
          secretKey: process.env.ALIYUN_ACCESS_KEY_SECRET,
          region: process.env.ALIYUN_REGION || 'cn-shanghai',
          enabled: !!process.env.ALIYUN_ACCESS_KEY_ID
        }
      },
      text: {
        qwen: {
          provider: 'qwen',
          apiKey: process.env.QWEN_API_KEY,
          baseUrl: 'https://dashscope.aliyuncs.com',
          model: process.env.QWEN_MODEL || 'qwen-turbo',
          enabled: !!process.env.QWEN_API_KEY
        },
        spark: {
          provider: 'spark',
          apiKey: process.env.SPARK_API_KEY,
          secretKey: process.env.SPARK_SECRET_KEY,
          appId: process.env.SPARK_APP_ID,
          baseUrl: 'https://spark-api.xf-yun.com',
          model: process.env.SPARK_MODEL || 'generalv3',
          enabled: !!process.env.SPARK_API_KEY
        },
        wenxin: {
          provider: 'wenxin',
          apiKey: process.env.WENXIN_API_KEY,
          secretKey: process.env.WENXIN_SECRET_KEY,
          baseUrl: 'https://aip.baidubce.com',
          model: process.env.WENXIN_MODEL || 'ernie-bot-turbo',
          enabled: !!process.env.WENXIN_API_KEY
        }
      }
    }
  }

  /**
   * 初始化所有可用的AI服务提供商
   */
  initializeProviders () {
    // 初始化图像识别服务
    this.initializeImageProviders()
    
    // 初始化文本生成服务
    this.initializeTextProviders()
  }

  /**
   * 初始化图像识别服务提供商
   */
  initializeImageProviders () {
    const imageConfigs = this.configs.image
    
    for (const [provider, config] of Object.entries(imageConfigs)) {
      if (config.enabled) {
        try {
          const ProviderClass = this.loadImageProvider(provider)
          const instance = new ProviderClass(config)
          instance.validateConfig()
          this.imageProviders.set(provider, instance)
          console.log(`✅ ${provider} 图像识别服务初始化成功`)
        } catch (error) {
          console.error(`❌ ${provider} 图像识别服务初始化失败:`, error.message)
        }
      }
    }
  }

  /**
   * 初始化文本生成服务提供商
   */
  initializeTextProviders () {
    const textConfigs = this.configs.text
    
    for (const [provider, config] of Object.entries(textConfigs)) {
      if (config.enabled) {
        try {
          const ProviderClass = this.loadTextProvider(provider)
          const instance = new ProviderClass(config)
          instance.validateConfig()
          this.textProviders.set(provider, instance)
          console.log(`✅ ${provider} 文本生成服务初始化成功`)
        } catch (error) {
          console.error(`❌ ${provider} 文本生成服务初始化失败:`, error.message)
        }
      }
    }
  }

  /**
   * 动态加载图像识别服务提供商类
   * @param {string} provider - 提供商名称
   * @returns {Class} 提供商类
   */
  loadImageProvider (provider) {
    const providerMap = {
      baidu: '../image/BaiduImageRecognition',
      tencent: '../image/TencentImageRecognition',
      aliyun: '../image/AliyunImageRecognition'
    }
    
    const modulePath = providerMap[provider]
    if (!modulePath) {
      throw new Error(`Unknown image provider: ${provider}`)
    }
    
    return require(modulePath)
  }

  /**
   * 动态加载文本生成服务提供商类
   * @param {string} provider - 提供商名称
   * @returns {Class} 提供商类
   */
  loadTextProvider (provider) {
    const providerMap = {
      qwen: '../text/QwenTextGeneration',
      spark: '../text/SparkTextGeneration',
      wenxin: '../text/WenxinTextGeneration'
    }
    
    const modulePath = providerMap[provider]
    if (!modulePath) {
      throw new Error(`Unknown text provider: ${provider}`)
    }
    
    return require(modulePath)
  }

  /**
   * 获取图像识别服务实例
   * @param {string} provider - 提供商名称，如果不指定则返回默认可用的
   * @returns {Object} 服务实例
   */
  getImageService (provider = null) {
    if (provider) {
      const service = this.imageProviders.get(provider)
      if (!service) {
        throw new Error(`Image provider ${provider} not available`)
      }
      return service
    }
    
    // 返回第一个可用的服务
    const availableServices = Array.from(this.imageProviders.values())
    if (availableServices.length === 0) {
      throw new Error('No image recognition services available')
    }
    
    return availableServices[0]
  }

  /**
   * 获取文本生成服务实例
   * @param {string} provider - 提供商名称，如果不指定则返回默认可用的
   * @returns {Object} 服务实例
   */
  getTextService (provider = null) {
    if (provider) {
      const service = this.textProviders.get(provider)
      if (!service) {
        throw new Error(`Text provider ${provider} not available`)
      }
      return service
    }
    
    // 返回第一个可用的服务
    const availableServices = Array.from(this.textProviders.values())
    if (availableServices.length === 0) {
      throw new Error('No text generation services available')
    }
    
    return availableServices[0]
  }

  /**
   * 获取所有可用的图像识别服务
   * @returns {Array} 服务列表
   */
  getAvailableImageServices () {
    return Array.from(this.imageProviders.keys())
  }

  /**
   * 获取所有可用的文本生成服务
   * @returns {Array} 服务列表
   */
  getAvailableTextServices () {
    return Array.from(this.textProviders.keys())
  }

  /**
   * 检查服务状态
   * @returns {Promise<Object>} 所有服务的状态
   */
  async checkServicesStatus () {
    const status = {
      image: {},
      text: {}
    }
    
    // 检查图像识别服务状态
    for (const [provider, service] of this.imageProviders) {
      try {
        status.image[provider] = await service.getStatus()
      } catch (error) {
        status.image[provider] = {
          provider,
          status: 'error',
          error: error.message
        }
      }
    }
    
    // 检查文本生成服务状态
    for (const [provider, service] of this.textProviders) {
      try {
        status.text[provider] = await service.getStatus()
      } catch (error) {
        status.text[provider] = {
          provider,
          status: 'error',
          error: error.message
        }
      }
    }
    
    return status
  }
}

// 创建单例实例
const aiServiceFactory = new AIServiceFactory()

module.exports = aiServiceFactory
