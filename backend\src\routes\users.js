const express = require('express')
const router = express.Router()
const userController = require('../controllers/userController')
const authMiddleware = require('../middleware/auth')
const validationMiddleware = require('../middleware/validation')
const { body, param, query } = require('express-validator')

// 用户信息验证规则
const updateProfileValidation = [
  body('nickname')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('昵称长度必须在1-50个字符之间'),
  body('avatar')
    .optional()
    .isURL()
    .withMessage('头像必须是有效的URL'),
  body('preferences')
    .optional()
    .isObject()
    .withMessage('偏好设置必须是对象格式'),
  body('health_profile')
    .optional()
    .isObject()
    .withMessage('健康档案必须是对象格式')
]

const favoriteValidation = [
  param('recipeId')
    .isInt({ min: 1 })
    .withMessage('菜谱ID必须是正整数'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('备注长度不能超过500个字符')
]

const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间')
]

// 所有用户相关路由都需要身份验证
router.use(authMiddleware)

// 获取用户信息
router.get('/profile', userController.getProfile)

// 更新用户信息
router.put('/profile', 
  updateProfileValidation,
  validationMiddleware,
  userController.updateProfile
)

// 获取用户收藏列表
router.get('/favorites',
  paginationValidation,
  validationMiddleware,
  userController.getFavorites
)

// 切换收藏状态
router.post('/favorites/:recipeId',
  favoriteValidation,
  validationMiddleware,
  userController.toggleFavorite
)

// 取消收藏
router.delete('/favorites/:recipeId',
  param('recipeId').isInt({ min: 1 }).withMessage('菜谱ID必须是正整数'),
  validationMiddleware,
  userController.toggleFavorite
)

// 获取用户统计信息
router.get('/stats', userController.getStats)

module.exports = router
