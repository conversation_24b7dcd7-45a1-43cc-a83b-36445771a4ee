const jwt = require('jsonwebtoken')
const { User } = require('../models').models

const authMiddleware = async (req, res, next) => {
  try {
    // 从请求头获取token
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '缺少认证token'
      })
    }
    
    const token = authHeader.substring(7) // 移除 'Bearer ' 前缀
    
    // 验证token
    let decoded
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET)
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Token已过期',
          code: 'TOKEN_EXPIRED'
        })
      } else if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: 'Token无效',
          code: 'TOKEN_INVALID'
        })
      } else {
        return res.status(401).json({
          success: false,
          message: 'Token验证失败',
          code: 'TOKEN_VERIFICATION_FAILED'
        })
      }
    }
    
    // 检查token类型
    if (decoded.type !== 'access_token') {
      return res.status(401).json({
        success: false,
        message: 'Token类型错误'
      })
    }
    
    // 验证用户是否存在且状态正常
    const user = await User.findByPk(decoded.userId)
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      })
    }
    
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户账号已被禁用'
      })
    }
    
    // 将用户信息添加到请求对象
    req.user = {
      userId: user.id,
      openid: user.openid,
      nickname: user.nickname,
      status: user.status
    }
    
    next()
  } catch (error) {
    console.error('认证中间件错误:', error)
    res.status(500).json({
      success: false,
      message: '认证服务异常'
    })
  }
}

// 可选的认证中间件（不强制要求登录）
const optionalAuthMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 没有token，继续执行，但不设置用户信息
      req.user = null
      return next()
    }
    
    const token = authHeader.substring(7)
    
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET)
      
      if (decoded.type === 'access_token') {
        const user = await User.findByPk(decoded.userId)
        if (user && user.status === 'active') {
          req.user = {
            userId: user.id,
            openid: user.openid,
            nickname: user.nickname,
            status: user.status
          }
        }
      }
    } catch (error) {
      // token无效，但不阻止请求继续
      console.log('可选认证token无效:', error.message)
    }
    
    req.user = req.user || null
    next()
  } catch (error) {
    console.error('可选认证中间件错误:', error)
    req.user = null
    next()
  }
}

module.exports = authMiddleware
module.exports.optionalAuth = optionalAuthMiddleware
