const { sequelize } = require('../config/database')

// 导入所有模型
const User = require('./User')
const Ingredient = require('./Ingredient')
const Recipe = require('./Recipe')
const RecognitionRecord = require('./RecognitionRecord')
const UserFavorite = require('./UserFavorite')
const AiUsageStats = require('./AiUsageStats')

// 定义模型关联关系
const defineAssociations = () => {
  // 用户与识别记录的关联
  User.hasMany(RecognitionRecord, {
    foreignKey: 'user_id',
    as: 'recognitionRecords'
  })
  RecognitionRecord.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  })

  // 用户与收藏的关联
  User.hasMany(UserFavorite, {
    foreignKey: 'user_id',
    as: 'favorites'
  })
  UserFavorite.belongsTo(User, {
    foreignKey: 'user_id',
    as: 'user'
  })

  // 菜谱与收藏的关联
  Recipe.hasMany(UserFavorite, {
    foreignKey: 'recipe_id',
    as: 'favorites'
  })
  UserFavorite.belongsTo(Recipe, {
    foreignKey: 'recipe_id',
    as: 'recipe'
  })

  // 用户与菜谱的多对多关联（通过收藏表）
  User.belongsToMany(Recipe, {
    through: UserFavorite,
    foreignKey: 'user_id',
    otherKey: 'recipe_id',
    as: 'favoriteRecipes'
  })
  Recipe.belongsToMany(User, {
    through: UserFavorite,
    foreignKey: 'recipe_id',
    otherKey: 'user_id',
    as: 'favoritedByUsers'
  })
}

// 初始化数据库
const initDatabase = async () => {
  try {
    // 定义关联关系
    defineAssociations()

    // 测试数据库连接
    await sequelize.authenticate()
    console.log('✅ Database connection has been established successfully.')

    // 同步数据库表结构（开发环境）
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true })
      console.log('✅ Database tables synchronized successfully.')
    }

    return true
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error)
    return false
  }
}

// 关闭数据库连接
const closeDatabase = async () => {
  try {
    await sequelize.close()
    console.log('✅ Database connection closed successfully.')
  } catch (error) {
    console.error('❌ Error closing database connection:', error)
  }
}

module.exports = {
  sequelize,
  models: {
    User,
    Ingredient,
    Recipe,
    RecognitionRecord,
    UserFavorite,
    AiUsageStats
  },
  initDatabase,
  closeDatabase,
  defineAssociations
}
