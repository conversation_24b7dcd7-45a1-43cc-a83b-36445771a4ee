const axios = require('axios')
const jwt = require('jsonwebtoken')
const { User } = require('../models').models

class AuthController {
  // 微信小程序登录
  async wxLogin (req, res) {
    try {
      const { code, userInfo } = req.body
      
      if (!code) {
        return res.status(400).json({
          success: false,
          message: '缺少登录凭证'
        })
      }
      
      // 调用微信API获取session_key和openid
      const wxResponse = await this.getWxSession(code)
      
      if (!wxResponse.openid) {
        return res.status(400).json({
          success: false,
          message: '微信登录失败'
        })
      }
      
      // 创建或更新用户信息
      const userData = {
        openid: wxResponse.openid,
        nickname: userInfo?.nickName || null,
        avatar: userInfo?.avatarUrl || null,
        last_login_at: new Date()
      }
      
      const user = await User.createOrUpdate(userData)
      
      // 生成JWT token
      const token = this.generateToken(user.id)
      
      res.json({
        success: true,
        message: '登录成功',
        data: {
          token,
          user: {
            id: user.id,
            nickname: user.nickname,
            avatar: user.avatar,
            preferences: user.preferences,
            health_profile: user.health_profile
          }
        }
      })
    } catch (error) {
      console.error('微信登录失败:', error)
      res.status(500).json({
        success: false,
        message: '登录失败，请重试'
      })
    }
  }

  // 刷新token
  async refreshToken (req, res) {
    try {
      const { userId } = req.user
      
      const user = await User.findByPk(userId)
      if (!user || user.status !== 'active') {
        return res.status(401).json({
          success: false,
          message: '用户不存在或已被禁用'
        })
      }
      
      const newToken = this.generateToken(user.id)
      
      res.json({
        success: true,
        message: 'Token刷新成功',
        data: {
          token: newToken
        }
      })
    } catch (error) {
      console.error('Token刷新失败:', error)
      res.status(500).json({
        success: false,
        message: 'Token刷新失败'
      })
    }
  }

  // 获取微信session信息
  async getWxSession (code) {
    try {
      const appId = process.env.WECHAT_APP_ID
      const appSecret = process.env.WECHAT_APP_SECRET
      
      const url = 'https://api.weixin.qq.com/sns/jscode2session'
      const params = {
        appid: appId,
        secret: appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      }
      
      const response = await axios.get(url, { params })
      
      if (response.data.errcode) {
        throw new Error(`微信API错误: ${response.data.errmsg}`)
      }
      
      return response.data
    } catch (error) {
      console.error('获取微信session失败:', error)
      throw error
    }
  }

  // 生成JWT token
  generateToken (userId) {
    const payload = {
      userId,
      type: 'access_token',
      iat: Math.floor(Date.now() / 1000)
    }
    
    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    })
  }

  // 验证token
  verifyToken (token) {
    try {
      return jwt.verify(token, process.env.JWT_SECRET)
    } catch (error) {
      throw new Error('Token验证失败')
    }
  }

  // 登出（可选，主要是清除客户端token）
  async logout (req, res) {
    try {
      // 这里可以添加token黑名单逻辑
      // 目前只是返回成功响应
      res.json({
        success: true,
        message: '登出成功'
      })
    } catch (error) {
      console.error('登出失败:', error)
      res.status(500).json({
        success: false,
        message: '登出失败'
      })
    }
  }
}

module.exports = new AuthController()
