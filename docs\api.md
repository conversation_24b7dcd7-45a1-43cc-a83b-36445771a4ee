# API接口文档

## 接口概览

### 基础信息
- **Base URL**: `https://api.airecipe.com/api`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: JWT Token

### 通用响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-12-19T10:30:00.000Z"
}
```

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": {
    "code": "ERROR_CODE",
    "details": "详细错误信息"
  },
  "timestamp": "2024-12-19T10:30:00.000Z"
}
```

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 认证接口

### 微信登录

**接口地址**: `POST /auth/wechat-login`

**请求参数**:
```json
{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "gender": 1,
    "city": "城市",
    "province": "省份",
    "country": "国家"
  }
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "JWT_TOKEN",
    "user": {
      "id": 1,
      "openid": "wx_openid",
      "nickname": "用户昵称",
      "avatar_url": "头像URL",
      "preferences": {},
      "health_profile": {},
      "created_at": "2024-12-19T10:30:00.000Z"
    }
  }
}
```

### 刷新Token

**接口地址**: `POST /auth/refresh-token`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**响应数据**:
```json
{
  "success": true,
  "message": "Token刷新成功",
  "data": {
    "token": "NEW_JWT_TOKEN",
    "expires_in": 7200
  }
}
```

## 用户接口

### 获取用户信息

**接口地址**: `GET /users/profile`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**响应数据**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "id": 1,
    "nickname": "用户昵称",
    "avatar_url": "头像URL",
    "preferences": {
      "cuisine_type": "川菜",
      "difficulty": "中等",
      "dietary_restrictions": ["素食"]
    },
    "health_profile": {
      "allergies": ["花生"],
      "health_goals": ["减肥"]
    },
    "stats": {
      "total_recognitions": 15,
      "total_favorites": 8,
      "total_recipes_generated": 12
    }
  }
}
```

### 更新用户信息

**接口地址**: `PUT /users/profile`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**请求参数**:
```json
{
  "nickname": "新昵称",
  "preferences": {
    "cuisine_type": "粤菜",
    "difficulty": "简单",
    "dietary_restrictions": ["无"]
  },
  "health_profile": {
    "allergies": [],
    "health_goals": ["健康饮食"]
  }
}
```

### 获取用户收藏

**接口地址**: `GET /users/favorites`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）

**响应数据**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "favorites": [
      {
        "id": 1,
        "recipe": {
          "id": 1,
          "name": "宫保鸡丁",
          "image_url": "图片URL",
          "difficulty": 2,
          "cook_time": 30,
          "rating": 4.5
        },
        "notes": "很好吃",
        "custom_tags": ["家常菜"],
        "created_at": "2024-12-19T10:30:00.000Z"
      }
    ],
    "pagination": {
      "total": 8,
      "page": 1,
      "limit": 20,
      "pages": 1
    }
  }
}
```

### 添加/取消收藏

**接口地址**: `POST /users/favorites/toggle`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**请求参数**:
```json
{
  "recipe_id": 1,
  "notes": "收藏备注",
  "custom_tags": ["标签1", "标签2"]
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "收藏成功",
  "data": {
    "is_favorited": true,
    "favorite_id": 1
  }
}
```

## AI服务接口

### 图像识别

**接口地址**: `POST /ai/recognize-image`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
Content-Type: multipart/form-data
```

**请求参数**:
- `image`: 图片文件（必需）
- `provider`: AI服务提供商（可选，baidu/tencent/aliyun）

**响应数据**:
```json
{
  "success": true,
  "message": "识别成功",
  "data": {
    "record_id": 1,
    "provider": "baidu",
    "processing_time": 1500,
    "ingredients": [
      {
        "name": "西红柿",
        "confidence": 0.95,
        "category": "蔬菜"
      },
      {
        "name": "鸡蛋",
        "confidence": 0.88,
        "category": "蛋类"
      }
    ],
    "confidence": 0.95
  }
}
```

### 生成菜谱

**接口地址**: `POST /ai/generate-recipe`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
Content-Type: application/json
```

**请求参数**:
```json
{
  "ingredients": ["西红柿", "鸡蛋", "葱"],
  "preferences": {
    "cuisine_type": "家常菜",
    "difficulty": "简单",
    "cook_time": 30,
    "servings": 2,
    "dietary_restrictions": []
  },
  "provider": "qwen"
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "菜谱生成成功",
  "data": {
    "provider": "qwen",
    "model": "qwen-turbo",
    "processing_time": 3000,
    "tokens_used": 1200,
    "recipe": {
      "name": "西红柿炒鸡蛋",
      "description": "经典家常菜，营养丰富，制作简单",
      "ingredients": [
        {
          "name": "西红柿",
          "amount": "2个",
          "unit": "个",
          "notes": "选择成熟的西红柿"
        },
        {
          "name": "鸡蛋",
          "amount": "3个",
          "unit": "个",
          "notes": "室温鸡蛋"
        }
      ],
      "steps": [
        {
          "step": 1,
          "description": "将西红柿洗净，切成块状",
          "time": "2分钟",
          "tips": "去皮效果更好"
        },
        {
          "step": 2,
          "description": "鸡蛋打散，加少许盐调味",
          "time": "1分钟",
          "tips": "充分打散"
        }
      ],
      "nutrition": {
        "calories": 180,
        "protein": 12,
        "carbs": 8,
        "fat": 10,
        "fiber": 2
      },
      "tags": ["家常菜", "简单", "营养"],
      "difficulty": 1,
      "cook_time": 15,
      "prep_time": 10,
      "servings": 2,
      "tips": "先炒鸡蛋再炒西红柿，口感更好"
    }
  }
}
```

### 获取AI服务状态

**接口地址**: `GET /ai/services/status`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**响应数据**:
```json
{
  "success": true,
  "message": "服务状态获取成功",
  "data": {
    "available_image_services": ["baidu", "tencent", "aliyun"],
    "available_text_services": ["qwen", "spark", "wenxin"],
    "status": {
      "image_services": {
        "baidu": {
          "status": "available",
          "response_time": 1200,
          "success_rate": 0.98
        },
        "tencent": {
          "status": "available",
          "response_time": 1500,
          "success_rate": 0.95
        }
      },
      "text_services": {
        "qwen": {
          "status": "available",
          "response_time": 2800,
          "success_rate": 0.99
        }
      }
    },
    "timestamp": "2024-12-19T10:30:00.000Z"
  }
}
```

### 获取识别历史

**接口地址**: `GET /ai/recognition/history`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）

**响应数据**:
```json
{
  "success": true,
  "message": "识别历史获取成功",
  "data": {
    "records": [
      {
        "id": 1,
        "image_url": "图片URL",
        "ai_provider": "baidu",
        "ai_model": "food_detect",
        "confidence_score": 0.95,
        "processing_time": 1500,
        "cost": 0.002,
        "status": "success",
        "created_at": "2024-12-19T10:30:00.000Z"
      }
    ],
    "pagination": {
      "total": 15,
      "page": 1,
      "limit": 20,
      "pages": 1
    }
  }
}
```

### 获取识别记录详情

**接口地址**: `GET /ai/recognition/:recordId`

**请求头**:
```
Authorization: Bearer JWT_TOKEN
```

**响应数据**:
```json
{
  "success": true,
  "message": "识别记录获取成功",
  "data": {
    "id": 1,
    "image_url": "图片URL",
    "ai_provider": "baidu",
    "ai_model": "food_detect",
    "recognition_result": {
      "ingredients": [
        {
          "name": "西红柿",
          "confidence": 0.95,
          "category": "蔬菜"
        }
      ],
      "confidence": 0.95
    },
    "confidence_score": 0.95,
    "processing_time": 1500,
    "cost": 0.002,
    "status": "success",
    "created_at": "2024-12-19T10:30:00.000Z"
  }
}
```

## 错误码说明

### 认证相关错误
- `AUTH_001`: 无效的微信code
- `AUTH_002`: Token已过期
- `AUTH_003`: Token无效
- `AUTH_004`: 用户不存在

### 参数验证错误
- `PARAM_001`: 必需参数缺失
- `PARAM_002`: 参数格式错误
- `PARAM_003`: 参数值超出范围
- `PARAM_004`: 文件格式不支持

### AI服务错误
- `AI_001`: AI服务不可用
- `AI_002`: 图像识别失败
- `AI_003`: 文本生成失败
- `AI_004`: 服务配额不足

### 业务逻辑错误
- `BIZ_001`: 用户未登录
- `BIZ_002`: 资源不存在
- `BIZ_003`: 操作权限不足
- `BIZ_004`: 重复操作

### 系统错误
- `SYS_001`: 数据库连接失败
- `SYS_002`: 缓存服务不可用
- `SYS_003`: 文件上传失败
- `SYS_004`: 外部服务调用失败

## 接口限流

### 限流规则
- **普通用户**: 100次/分钟
- **VIP用户**: 500次/分钟
- **图像识别**: 20次/分钟
- **菜谱生成**: 10次/分钟

### 限流响应
```json
{
  "success": false,
  "message": "请求过于频繁，请稍后再试",
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "details": "超出限流阈值"
  },
  "retry_after": 60
}
```

## 接口版本

### 版本控制
- 当前版本: `v1`
- 版本格式: `/api/v1/endpoint`
- 向后兼容: 支持旧版本6个月

### 版本变更
- **v1.0**: 初始版本
- **v1.1**: 增加AI服务状态接口
- **v1.2**: 优化菜谱生成接口
