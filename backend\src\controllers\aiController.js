const aiServiceFactory = require('../ai/factory/AIServiceFactory')
const { RecognitionRecord, AiUsageStats } = require('../models').models
const { asyncHandler } = require('../middleware/errorHandler')

class AIController {
  /**
   * 图像识别接口
   */
  recognizeImage = asyncHandler(async (req, res) => {
    const { userId } = req.user
    const { provider } = req.query
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传图片文件'
      })
    }

    const startTime = Date.now()
    let recognitionRecord = null

    try {
      // 获取图像识别服务
      const imageService = aiServiceFactory.getImageService(provider)
      
      // 创建识别记录
      recognitionRecord = await RecognitionRecord.create({
        user_id: userId,
        image_url: req.file.path || req.file.filename,
        ai_provider: imageService.provider,
        ai_model: imageService.model || 'default',
        status: 'pending'
      })

      // 执行图像识别
      const result = await imageService.recognizeIngredients(req.file.buffer)
      const processingTime = Date.now() - startTime

      if (result.success) {
        // 更新识别记录
        await recognitionRecord.update({
          recognition_result: result.data,
          processing_time: processingTime,
          confidence_score: result.data.confidence,
          status: 'success',
          cost: imageService.calculateCost(result)
        })

        // 记录AI使用统计
        await AiUsageStats.recordUsage({
          serviceType: 'image',
          provider: imageService.provider,
          modelName: imageService.model,
          cost: imageService.calculateCost(result),
          responseTime: processingTime,
          success: true
        })

        res.json({
          success: true,
          message: '图像识别成功',
          data: {
            record_id: recognitionRecord.id,
            provider: imageService.provider,
            processing_time: processingTime,
            ingredients: result.data.ingredients,
            confidence: result.data.confidence
          }
        })
      } else {
        // 识别失败
        await recognitionRecord.update({
          status: 'failed',
          error_message: result.error?.message || '识别失败',
          processing_time: processingTime
        })

        // 记录失败统计
        await AiUsageStats.recordUsage({
          serviceType: 'image',
          provider: imageService.provider,
          modelName: imageService.model,
          responseTime: processingTime,
          success: false
        })

        res.status(500).json({
          success: false,
          message: '图像识别失败',
          error: result.error?.message || '未知错误'
        })
      }
    } catch (error) {
      console.error('图像识别接口错误:', error)
      
      if (recognitionRecord) {
        await recognitionRecord.update({
          status: 'failed',
          error_message: error.message,
          processing_time: Date.now() - startTime
        })
      }

      res.status(500).json({
        success: false,
        message: '图像识别服务异常',
        error: error.message
      })
    }
  })

  /**
   * 生成菜谱接口
   */
  generateRecipe = asyncHandler(async (req, res) => {
    const { userId } = req.user
    const { ingredients, preferences = {}, provider } = req.body

    if (!ingredients || !Array.isArray(ingredients) || ingredients.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供食材列表'
      })
    }

    const startTime = Date.now()

    try {
      // 获取文本生成服务
      const textService = aiServiceFactory.getTextService(provider)
      
      // 生成菜谱
      const result = await textService.generateRecipe(ingredients, preferences)
      const processingTime = Date.now() - startTime

      if (result.success) {
        // 记录AI使用统计
        await AiUsageStats.recordUsage({
          serviceType: 'text',
          provider: textService.provider,
          modelName: textService.model,
          tokensUsed: result.tokens_used || 0,
          cost: textService.calculateCost(result.tokens_used || 0),
          responseTime: processingTime,
          success: true
        })

        res.json({
          success: true,
          message: '菜谱生成成功',
          data: {
            provider: textService.provider,
            model: textService.model,
            processing_time: processingTime,
            tokens_used: result.tokens_used,
            recipe: result.data
          }
        })
      } else {
        // 生成失败
        await AiUsageStats.recordUsage({
          serviceType: 'text',
          provider: textService.provider,
          modelName: textService.model,
          responseTime: processingTime,
          success: false
        })

        res.status(500).json({
          success: false,
          message: '菜谱生成失败',
          error: result.error?.message || '未知错误'
        })
      }
    } catch (error) {
      console.error('菜谱生成接口错误:', error)
      
      res.status(500).json({
        success: false,
        message: '菜谱生成服务异常',
        error: error.message
      })
    }
  })

  /**
   * 获取AI服务状态
   */
  getServicesStatus = asyncHandler(async (req, res) => {
    try {
      const status = await aiServiceFactory.checkServicesStatus()
      
      res.json({
        success: true,
        message: '服务状态获取成功',
        data: {
          available_image_services: aiServiceFactory.getAvailableImageServices(),
          available_text_services: aiServiceFactory.getAvailableTextServices(),
          status: status,
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('获取AI服务状态失败:', error)
      
      res.status(500).json({
        success: false,
        message: '获取服务状态失败',
        error: error.message
      })
    }
  })

  /**
   * 获取AI使用统计
   */
  getUsageStats = asyncHandler(async (req, res) => {
    try {
      const { startDate, endDate } = req.query
      
      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          message: '请提供开始和结束日期'
        })
      }

      const stats = await AiUsageStats.getDailyStats(startDate, endDate)
      
      res.json({
        success: true,
        message: '统计数据获取成功',
        data: {
          period: { startDate, endDate },
          stats: stats
        }
      })
    } catch (error) {
      console.error('获取AI使用统计失败:', error)
      
      res.status(500).json({
        success: false,
        message: '获取统计数据失败',
        error: error.message
      })
    }
  })

  /**
   * 获取识别历史记录
   */
  getRecognitionHistory = asyncHandler(async (req, res) => {
    try {
      const { userId } = req.user
      const { page = 1, limit = 20 } = req.query
      
      const offset = (page - 1) * limit
      
      const records = await RecognitionRecord.findAndCountAll({
        where: { user_id: userId },
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: offset,
        attributes: {
          exclude: ['recognition_result'] // 排除大字段以提高性能
        }
      })
      
      res.json({
        success: true,
        message: '识别历史获取成功',
        data: {
          records: records.rows,
          pagination: {
            total: records.count,
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(records.count / limit)
          }
        }
      })
    } catch (error) {
      console.error('获取识别历史失败:', error)
      
      res.status(500).json({
        success: false,
        message: '获取识别历史失败',
        error: error.message
      })
    }
  })
}

module.exports = new AIController()
