/* 首页样式 */
.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 欢迎区域 */
.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  color: white;
}

.welcome-text {
  flex: 1;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

.welcome-image {
  width: 120rpx;
  height: 120rpx;
}

/* 功能区域 */
.function-section {
  margin-bottom: 40rpx;
}

.function-card {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 20rpx;
}

.main-card {
  border: 2rpx solid #4CAF50;
}

.card-icon {
  margin-right: 20rpx;
}

.card-content {
  flex: 1;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.card-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 功能网格 */
.function-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.grid-item text {
  font-size: 22rpx;
  color: #333;
  margin-top: 12rpx;
}

/* 推荐菜谱区域 */
.recommend-section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #4CAF50;
}

.recipe-scroll {
  white-space: nowrap;
}

.recipe-list {
  display: flex;
  gap: 20rpx;
  padding-bottom: 10rpx;
}

.recipe-item {
  display: inline-block;
  width: 280rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.recipe-image {
  width: 100%;
  height: 200rpx;
}

.recipe-info {
  padding: 20rpx;
}

.recipe-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recipe-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recipe-time {
  font-size: 22rpx;
  color: #666;
}

.recipe-rating {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 22rpx;
  color: #666;
}

/* 最近识别区域 */
.recent-section {
  margin-bottom: 40rpx;
}

.recent-list {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.recent-info {
  flex: 1;
}

.recent-ingredients {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recent-time {
  font-size: 22rpx;
  color: #999;
}

/* 快速开始提示 */
.quick-start {
  margin-top: 40rpx;
}

.quick-start-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 30rpx;
  background: #e3f2fd;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #2196F3;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .function-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-item {
    padding: 24rpx 16rpx;
  }
  
  .recipe-item {
    width: 240rpx;
  }
}

/* 动画效果 */
.function-card {
  transition: transform 0.2s ease;
}

.function-card:active {
  transform: scale(0.98);
}

.grid-item {
  transition: transform 0.2s ease;
}

.grid-item:active {
  transform: scale(0.95);
}

.recipe-item {
  transition: transform 0.2s ease;
}

.recipe-item:active {
  transform: scale(0.98);
}
