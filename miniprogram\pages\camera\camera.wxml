<!--拍照识别页面-->
<view class="container">
  <!-- 相机预览区域 -->
  <view class="camera-section" wx:if="{{!selectedImage}}">
    <camera 
      class="camera"
      device-position="{{cameraPosition}}"
      flash="{{flashMode}}"
      binderror="onCameraError"
      bindstop="onCameraStop"
      bindinitdone="onCameraReady"
    ></camera>
    
    <!-- 相机控制层 -->
    <view class="camera-controls">
      <!-- 顶部控制栏 -->
      <view class="top-controls">
        <view class="control-item" bindtap="toggleFlash">
          <van-icon 
            name="{{flashMode === 'on' ? 'flash' : 'flash-o'}}" 
            size="24" 
            color="white" 
          />
          <text>闪光灯</text>
        </view>
        <view class="control-item" bindtap="switchCamera">
          <van-icon name="replay" size="24" color="white" />
          <text>切换</text>
        </view>
      </view>
      
      <!-- 底部拍照区域 -->
      <view class="bottom-controls">
        <view class="album-btn" bindtap="chooseFromAlbum">
          <van-icon name="photo-o" size="24" color="white" />
          <text>相册</text>
        </view>
        
        <view class="capture-btn" bindtap="takePhoto">
          <view class="capture-inner"></view>
        </view>
        
        <view class="help-btn" bindtap="showHelp">
          <van-icon name="question-o" size="24" color="white" />
          <text>帮助</text>
        </view>
      </view>
    </view>
    
    <!-- 拍照提示 -->
    <view class="camera-tips">
      <text>请将食材放在取景框内，确保光线充足</text>
    </view>
  </view>

  <!-- 图片预览区域 -->
  <view class="preview-section" wx:if="{{selectedImage}}">
    <view class="preview-header">
      <van-icon name="arrow-left" size="20" bindtap="backToCamera" />
      <text>图片预览</text>
      <text class="confirm-btn" bindtap="confirmImage">确认</text>
    </view>
    
    <view class="preview-container">
      <image 
        class="preview-image" 
        src="{{selectedImage}}" 
        mode="aspectFit"
        bindload="onImageLoad"
      ></image>
      
      <!-- 图片操作按钮 -->
      <view class="image-actions">
        <view class="action-btn" bindtap="retakePhoto">
          <van-icon name="photograph" size="20" />
          <text>重拍</text>
        </view>
        <view class="action-btn" bindtap="chooseFromAlbum">
          <van-icon name="photo-o" size="20" />
          <text>相册</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 识别中状态 -->
  <view class="recognizing-section" wx:if="{{recognizing}}">
    <view class="recognizing-content">
      <van-loading type="spinner" size="40" color="#4CAF50" />
      <text class="recognizing-text">AI正在识别中...</text>
      <text class="recognizing-tip">{{recognizingTip}}</text>
    </view>
  </view>

  <!-- 权限提示 -->
  <view class="permission-section" wx:if="{{showPermissionTip}}">
    <view class="permission-content">
      <van-icon name="warning-o" size="40" color="#FF9800" />
      <text class="permission-title">需要相机权限</text>
      <text class="permission-desc">请在设置中开启相机权限，以便拍照识别食材</text>
      <view class="permission-actions">
        <van-button type="default" size="small" bindtap="hidePermissionTip">
          取消
        </van-button>
        <van-button type="primary" size="small" bindtap="openSettings">
          去设置
        </van-button>
      </view>
    </view>
  </view>
</view>

<!-- 帮助弹窗 -->
<van-popup 
  show="{{showHelpModal}}" 
  position="bottom" 
  round
  bind:close="hideHelp"
>
  <view class="help-modal">
    <view class="help-header">
      <text class="help-title">拍照识别小贴士</text>
      <van-icon name="cross" size="18" bindtap="hideHelp" />
    </view>
    
    <view class="help-content">
      <view class="help-item">
        <van-icon name="bulb-o" size="16" color="#4CAF50" />
        <text>确保光线充足，避免阴影遮挡</text>
      </view>
      <view class="help-item">
        <van-icon name="aim" size="16" color="#4CAF50" />
        <text>将食材完整放在取景框内</text>
      </view>
      <view class="help-item">
        <van-icon name="photo-o" size="16" color="#4CAF50" />
        <text>保持手机稳定，避免模糊</text>
      </view>
      <view class="help-item">
        <van-icon name="star-o" size="16" color="#4CAF50" />
        <text>单独拍摄效果更佳</text>
      </view>
    </view>
  </view>
</van-popup>

<!-- 全局提示 -->
<van-toast id="van-toast" />
<van-dialog id="van-dialog" />
