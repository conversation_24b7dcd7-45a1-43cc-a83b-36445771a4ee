const tencentcloud = require('tencentcloud-sdk-nodejs')
const BaseImageRecognition = require('../base/BaseImageRecognition')

/**
 * 腾讯云AI图像识别服务适配器
 */
class TencentImageRecognition extends BaseImageRecognition {
  constructor (config) {
    super(config)
    
    // 初始化腾讯云客户端
    const TiiaClient = tencentcloud.tiia.v20190529.Client
    const clientConfig = {
      credential: {
        secretId: this.apiKey,
        secretKey: this.secretKey
      },
      region: config.region || 'ap-beijing',
      profile: {
        httpProfile: {
          endpoint: 'tiia.tencentcloudapi.com'
        }
      }
    }
    
    this.client = new TiiaClient(clientConfig)
  }

  /**
   * 识别图片中的食材
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeIngredients (image, options = {}) {
    try {
      const base64Image = this.preprocessImage(image)
      
      // 使用腾讯云通用物体识别
      const params = {
        ImageBase64: base64Image
      }
      
      const response = await this.client.DetectLabel(params)
      return this.normalizeIngredientsResult(response)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 识别菜品
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeDish (image, options = {}) {
    try {
      const base64Image = this.preprocessImage(image)
      
      // 使用腾讯云菜品识别
      const params = {
        ImageBase64: base64Image
      }
      
      const response = await this.client.RecognizeFood(params)
      return this.normalizeDishResult(response)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 通用物体识别
   * @param {string|Buffer} image - 图片数据
   * @param {Object} options - 识别选项
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeObject (image, options = {}) {
    try {
      const base64Image = this.preprocessImage(image)
      
      const params = {
        ImageBase64: base64Image,
        Scenes: ['WEB', 'CAMERA']
      }
      
      const response = await this.client.DetectLabel(params)
      return this.normalizeObjectResult(response)
    } catch (error) {
      return this.handleError(error)
    }
  }

  /**
   * 标准化食材识别结果
   * @param {Object} rawResult - 腾讯云原始结果
   * @returns {Object} 标准化结果
   */
  normalizeIngredientsResult (rawResult) {
    const ingredients = []
    
    if (rawResult.Labels && rawResult.Labels.length > 0) {
      for (const label of rawResult.Labels) {
        // 过滤出食物相关的标签
        if (this.isFoodRelated(label.Name)) {
          ingredients.push({
            name: label.Name,
            confidence: label.Confidence / 100, // 转换为0-1范围
            category: this.categorizeIngredient(label.Name),
            first_category: label.FirstCategory,
            second_category: label.SecondCategory
          })
        }
      }
    }

    return this.normalizeResult({
      ingredients,
      confidence: ingredients.length > 0 ? ingredients[0].confidence : 0,
      raw_result: rawResult
    })
  }

  /**
   * 标准化菜品识别结果
   * @param {Object} rawResult - 腾讯云原始结果
   * @returns {Object} 标准化结果
   */
  normalizeDishResult (rawResult) {
    const dishes = []
    
    if (rawResult.RegionDetected && rawResult.RegionDetected.length > 0) {
      for (const region of rawResult.RegionDetected) {
        if (region.Food && region.Food.length > 0) {
          for (const food of region.Food) {
            dishes.push({
              name: food.Name,
              confidence: food.Confidence / 100,
              calorie: food.Calorie || null,
              category: food.Category || null
            })
          }
        }
      }
    }

    return this.normalizeResult({
      dishes,
      confidence: dishes.length > 0 ? dishes[0].confidence : 0,
      raw_result: rawResult
    })
  }

  /**
   * 标准化物体识别结果
   * @param {Object} rawResult - 腾讯云原始结果
   * @returns {Object} 标准化结果
   */
  normalizeObjectResult (rawResult) {
    const objects = []
    
    if (rawResult.Labels && rawResult.Labels.length > 0) {
      for (const label of rawResult.Labels) {
        objects.push({
          name: label.Name,
          confidence: label.Confidence / 100,
          first_category: label.FirstCategory,
          second_category: label.SecondCategory
        })
      }
    }

    return this.normalizeResult({
      objects,
      confidence: objects.length > 0 ? objects[0].confidence : 0,
      raw_result: rawResult
    })
  }

  /**
   * 判断是否为食物相关
   * @param {string} name - 标签名称
   * @returns {boolean} 是否为食物相关
   */
  isFoodRelated (name) {
    const foodCategories = [
      '食物', '蔬菜', '水果', '肉类', '海鲜', '谷物', '豆类', '坚果',
      '调料', '饮料', '甜品', '面食', '米饭', '汤', '菜肴'
    ]
    
    const foodKeywords = [
      '菜', '肉', '鱼', '虾', '蟹', '蛋', '奶', '米', '面', '豆',
      '果', '瓜', '萝卜', '白菜', '菠菜', '芹菜', '韭菜', '茄子',
      '番茄', '黄瓜', '土豆', '红薯', '玉米', '苹果', '香蕉',
      '橙子', '葡萄', '草莓', '西瓜', '梨', '桃', '李', '杏'
    ]
    
    // 检查分类
    for (const category of foodCategories) {
      if (name.includes(category)) {
        return true
      }
    }
    
    // 检查关键词
    for (const keyword of foodKeywords) {
      if (name.includes(keyword)) {
        return true
      }
    }
    
    return false
  }

  /**
   * 食材分类
   * @param {string} ingredient - 食材名称
   * @returns {string} 分类
   */
  categorizeIngredient (ingredient) {
    const categories = {
      蔬菜: ['菜', '萝卜', '白菜', '菠菜', '芹菜', '韭菜', '豆角', '茄子', '番茄', '黄瓜', '冬瓜', '南瓜', '土豆', '红薯'],
      水果: ['苹果', '香蕉', '橙子', '葡萄', '草莓', '西瓜', '梨', '桃', '李', '杏', '柠檬', '柚子'],
      肉类: ['猪肉', '牛肉', '羊肉', '鸡肉', '鸭肉', '鹅肉', '肉'],
      海鲜: ['鱼', '虾', '蟹', '贝', '海带', '紫菜', '海鲜'],
      谷物: ['米', '面', '麦', '玉米', '小米', '燕麦', '谷物'],
      调料: ['盐', '糖', '醋', '酱油', '料酒', '胡椒', '花椒', '八角', '调料'],
      豆类: ['豆', '豆腐', '豆浆', '豆芽'],
      蛋奶: ['蛋', '奶', '酸奶', '奶酪']
    }
    
    for (const [category, keywords] of Object.entries(categories)) {
      for (const keyword of keywords) {
        if (ingredient.includes(keyword)) {
          return category
        }
      }
    }
    
    return '其他'
  }

  /**
   * 计算调用成本
   * @param {Object} result - 识别结果
   * @returns {number} 成本（元）
   */
  calculateCost (result) {
    // 腾讯云图像识别按次计费，约0.0015元/次
    return 0.0015
  }

  /**
   * 获取服务状态
   * @returns {Promise<Object>} 服务状态
   */
  async getStatus () {
    try {
      // 尝试调用一个简单的API来检查服务状态
      const params = {
        ImageBase64: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==' // 1x1像素的透明图片
      }
      
      await this.client.DetectLabel(params)
      
      return {
        provider: this.provider,
        status: 'available',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        provider: this.provider,
        status: 'unavailable',
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }
}

module.exports = TencentImageRecognition
