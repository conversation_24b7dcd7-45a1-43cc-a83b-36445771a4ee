'use strict'

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('ai_usage_stats', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      service_type: {
        type: Sequelize.ENUM('image', 'text'),
        allowNull: false,
        comment: '服务类型'
      },
      provider: {
        type: Sequelize.STRING(20),
        allowNull: false,
        comment: 'AI服务提供商'
      },
      model_name: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: '模型名称'
      },
      tokens_used: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '使用的token数量'
      },
      requests_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '请求次数'
      },
      success_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '成功次数'
      },
      error_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '错误次数'
      },
      cost: {
        type: Sequelize.DECIMAL(10, 4),
        allowNull: false,
        defaultValue: 0,
        comment: '成本'
      },
      avg_response_time: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '平均响应时间(毫秒)'
      },
      date: {
        type: Sequelize.DATEONLY,
        allowNull: false,
        comment: '统计日期'
      },
      hour: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '统计小时(0-23)'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    })

    // 添加索引
    await queryInterface.addIndex('ai_usage_stats', ['service_type', 'provider', 'date', 'hour'], {
      unique: true,
      name: 'ai_usage_stats_unique'
    })
    await queryInterface.addIndex('ai_usage_stats', ['service_type'])
    await queryInterface.addIndex('ai_usage_stats', ['provider'])
    await queryInterface.addIndex('ai_usage_stats', ['date'])
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('ai_usage_stats')
  }
}
