const { DataTypes } = require('sequelize')
const { sequelize } = require('../config/database')

const RecognitionRecord = sequelize.define('RecognitionRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID',
    references: {
      model: 'users',
      key: 'id'
    }
  },
  image_url: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '上传的图片URL'
  },
  recognition_result: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: '识别结果',
    defaultValue: {}
    // 格式: { ingredients: [{ name: '鸡蛋', confidence: 0.95 }], raw_result: {} }
  },
  ai_provider: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: 'AI服务提供商'
  },
  ai_model: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'AI模型名称'
  },
  processing_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '处理时间(毫秒)'
  },
  confidence_score: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: true,
    comment: '置信度分数'
  },
  status: {
    type: DataTypes.ENUM('pending', 'success', 'failed'),
    defaultValue: 'pending',
    comment: '识别状态'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '错误信息'
  },
  cost: {
    type: DataTypes.DECIMAL(10, 6),
    allowNull: true,
    comment: '调用成本'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'recognition_records',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['ai_provider']
    },
    {
      fields: ['status']
    },
    {
      fields: ['created_at']
    }
  ]
})

// 类方法
RecognitionRecord.findByUser = function (userId, limit = 10) {
  return this.findAll({
    where: { user_id: userId },
    order: [['created_at', 'DESC']],
    limit: limit
  })
}

RecognitionRecord.getStatsByProvider = function (startDate, endDate) {
  return this.findAll({
    attributes: [
      'ai_provider',
      [sequelize.fn('COUNT', sequelize.col('id')), 'total_calls'],
      [sequelize.fn('SUM', sequelize.col('cost')), 'total_cost'],
      [sequelize.fn('AVG', sequelize.col('processing_time')), 'avg_processing_time'],
      [sequelize.fn('AVG', sequelize.col('confidence_score')), 'avg_confidence']
    ],
    where: {
      created_at: {
        [sequelize.Op.between]: [startDate, endDate]
      },
      status: 'success'
    },
    group: ['ai_provider']
  })
}

module.exports = RecognitionRecord
