<!--首页-->
<view class="container">
  <!-- 顶部欢迎区域 -->
  <view class="welcome-section">
    <view class="welcome-text">
      <text class="title">智能菜谱助手</text>
      <text class="subtitle">拍照识别食材，AI生成美味菜谱</text>
    </view>
    <image class="welcome-image" src="/images/cooking.png" mode="aspectFit"></image>
  </view>

  <!-- 功能入口区域 -->
  <view class="function-section">
    <!-- 拍照识别 -->
    <view class="function-card main-card" bindtap="goToCamera">
      <view class="card-icon">
        <van-icon name="photograph" size="40" color="#4CAF50" />
      </view>
      <view class="card-content">
        <text class="card-title">拍照识别</text>
        <text class="card-desc">拍摄食材照片，智能识别食材种类</text>
      </view>
      <van-icon name="arrow" size="16" color="#999" />
    </view>

    <!-- 功能网格 -->
    <view class="function-grid">
      <view class="grid-item" bindtap="goToRecipes">
        <van-icon name="apps-o" size="24" color="#FF9800" />
        <text>菜谱大全</text>
      </view>
      <view class="grid-item" bindtap="goToFavorites">
        <van-icon name="star-o" size="24" color="#E91E63" />
        <text>我的收藏</text>
      </view>
      <view class="grid-item" bindtap="goToHistory">
        <van-icon name="clock-o" size="24" color="#2196F3" />
        <text>识别历史</text>
      </view>
      <view class="grid-item" bindtap="goToProfile">
        <van-icon name="user-o" size="24" color="#9C27B0" />
        <text>个人中心</text>
      </view>
    </view>
  </view>

  <!-- 推荐菜谱区域 -->
  <view class="recommend-section">
    <view class="section-header">
      <text class="section-title">推荐菜谱</text>
      <text class="section-more" bindtap="goToRecipes">更多 ></text>
    </view>
    
    <scroll-view class="recipe-scroll" scroll-x="true" show-scrollbar="false">
      <view class="recipe-list">
        <view 
          class="recipe-item" 
          wx:for="{{recommendRecipes}}" 
          wx:key="id"
          bindtap="goToRecipeDetail"
          data-id="{{item.id}}"
        >
          <image class="recipe-image" src="{{item.image_url}}" mode="aspectFill"></image>
          <view class="recipe-info">
            <text class="recipe-name">{{item.name}}</text>
            <view class="recipe-meta">
              <text class="recipe-time">{{item.cook_time}}分钟</text>
              <view class="recipe-rating">
                <van-icon name="star" size="12" color="#FFD700" />
                <text>{{item.rating}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 最近识别区域 -->
  <view class="recent-section" wx:if="{{recentRecognitions.length > 0}}">
    <view class="section-header">
      <text class="section-title">最近识别</text>
      <text class="section-more" bindtap="goToHistory">更多 ></text>
    </view>
    
    <view class="recent-list">
      <view 
        class="recent-item" 
        wx:for="{{recentRecognitions}}" 
        wx:key="id"
        bindtap="goToRecognitionDetail"
        data-id="{{item.id}}"
      >
        <image class="recent-image" src="{{item.image_url}}" mode="aspectFill"></image>
        <view class="recent-info">
          <text class="recent-ingredients">{{item.ingredients}}</text>
          <text class="recent-time">{{item.time}}</text>
        </view>
        <van-icon name="arrow" size="16" color="#999" />
      </view>
    </view>
  </view>

  <!-- 快速开始提示 -->
  <view class="quick-start" wx:if="{{!hasUsed}}">
    <view class="quick-start-content">
      <van-icon name="info-o" size="20" color="#2196F3" />
      <text>点击"拍照识别"开始您的智能烹饪之旅</text>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<van-loading wx:if="{{loading}}" type="spinner" color="#4CAF50">加载中...</van-loading>

<!-- 空状态 -->
<van-empty 
  wx:if="{{!loading && recommendRecipes.length === 0}}" 
  image="search" 
  description="暂无推荐菜谱"
/>

<!-- 全局提示 -->
<van-toast id="van-toast" />
