const { User, UserFavorite, Recipe } = require('../models').models
const { redisUtils } = require('../config/redis')

class UserController {
  // 获取用户信息
  async getProfile (req, res) {
    try {
      const { userId } = req.user
      
      // 先从缓存获取
      const cacheKey = `user:profile:${userId}`
      let user = await redisUtils.get(cacheKey)
      
      if (!user) {
        user = await User.findByPk(userId, {
          attributes: { exclude: ['openid'] }
        })
        
        if (!user) {
          return res.status(404).json({
            success: false,
            message: '用户不存在'
          })
        }
        
        // 缓存用户信息
        await redisUtils.set(cacheKey, user, 3600) // 1小时
      }
      
      res.json({
        success: true,
        data: user
      })
    } catch (error) {
      console.error('获取用户信息失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户信息失败'
      })
    }
  }

  // 更新用户信息
  async updateProfile (req, res) {
    try {
      const { userId } = req.user
      const { nickname, avatar, preferences, health_profile } = req.body
      
      const user = await User.findByPk(userId)
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        })
      }
      
      const updateData = {}
      if (nickname !== undefined) updateData.nickname = nickname
      if (avatar !== undefined) updateData.avatar = avatar
      if (preferences !== undefined) updateData.preferences = preferences
      if (health_profile !== undefined) updateData.health_profile = health_profile
      
      await user.update(updateData)
      
      // 清除缓存
      const cacheKey = `user:profile:${userId}`
      await redisUtils.del(cacheKey)
      
      res.json({
        success: true,
        message: '用户信息更新成功',
        data: user
      })
    } catch (error) {
      console.error('更新用户信息失败:', error)
      res.status(500).json({
        success: false,
        message: '更新用户信息失败'
      })
    }
  }

  // 获取用户收藏列表
  async getFavorites (req, res) {
    try {
      const { userId } = req.user
      const { page = 1, limit = 20 } = req.query
      
      const offset = (page - 1) * limit
      
      const favorites = await UserFavorite.findAndCountAll({
        where: { user_id: userId },
        include: [{
          model: Recipe,
          as: 'recipe',
          attributes: ['id', 'name', 'description', 'image_url', 'difficulty', 'cook_time', 'rating']
        }],
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: offset
      })
      
      res.json({
        success: true,
        data: {
          favorites: favorites.rows,
          pagination: {
            total: favorites.count,
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(favorites.count / limit)
          }
        }
      })
    } catch (error) {
      console.error('获取收藏列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取收藏列表失败'
      })
    }
  }

  // 切换收藏状态
  async toggleFavorite (req, res) {
    try {
      const { userId } = req.user
      const { recipeId } = req.params
      const { notes } = req.body
      
      const result = await UserFavorite.toggleFavorite(userId, recipeId, notes)
      
      // 更新菜谱收藏数
      const recipe = await Recipe.findByPk(recipeId)
      if (recipe) {
        if (result.action === 'added') {
          await recipe.increment('favorite_count')
        } else {
          await recipe.decrement('favorite_count')
        }
      }
      
      res.json({
        success: true,
        message: result.action === 'added' ? '收藏成功' : '取消收藏成功',
        data: result
      })
    } catch (error) {
      console.error('切换收藏状态失败:', error)
      res.status(500).json({
        success: false,
        message: '操作失败'
      })
    }
  }

  // 获取用户统计信息
  async getStats (req, res) {
    try {
      const { userId } = req.user
      
      const cacheKey = `user:stats:${userId}`
      let stats = await redisUtils.get(cacheKey)
      
      if (!stats) {
        const [favoriteCount, recognitionCount] = await Promise.all([
          UserFavorite.count({ where: { user_id: userId } }),
          // 这里需要导入 RecognitionRecord 模型
          // RecognitionRecord.count({ where: { user_id: userId } })
          0 // 临时返回0
        ])
        
        stats = {
          favorite_count: favoriteCount,
          recognition_count: recognitionCount
        }
        
        // 缓存统计信息
        await redisUtils.set(cacheKey, stats, 1800) // 30分钟
      }
      
      res.json({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('获取用户统计失败:', error)
      res.status(500).json({
        success: false,
        message: '获取统计信息失败'
      })
    }
  }
}

module.exports = new UserController()
